-- =====================================================
-- Malombo Admin Panel - Accommodations Table Setup
-- =====================================================
-- This script creates the accommodations table and related infrastructure
-- for managing accommodation listings in the admin panel.

-- Drop existing table if it exists (for development)
DROP TABLE IF EXISTS public.accommodations CASCADE;

-- Create accommodations table
CREATE TABLE public.accommodations (
    -- Primary key
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- Basic information
    name TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN (
        'banda', 'room', 'tent', 'tree_house', 
        'campsite', 'suite', 'lodge', 'cabin'
    )),
    
    -- Descriptions
    description TEXT NOT NULL,
    special_features TEXT,
    
    -- Amenities as JSON array
    amenities JSONB DEFAULT '[]'::jsonb,
    
    -- Pricing and capacity
    price_range TEXT NOT NULL, -- e.g., "$180–$380"
    capacity INTEGER NOT NULL CHECK (capacity > 0),
    
    -- Images stored as array of Supabase storage URLs
    images JSONB DEFAULT '[]'::jsonb,
    
    -- Status and visibility
    status TEXT NOT NULL CHECK (status IN ('draft', 'published', 'unpublished')) DEFAULT 'draft',
    featured BOOLEAN NOT NULL DEFAULT false,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id)
);

-- Create indexes for better performance
CREATE INDEX idx_accommodations_status ON public.accommodations(status);
CREATE INDEX idx_accommodations_type ON public.accommodations(type);
CREATE INDEX idx_accommodations_featured ON public.accommodations(featured);
CREATE INDEX idx_accommodations_created_at ON public.accommodations(created_at);

-- Enable Row Level Security
ALTER TABLE public.accommodations ENABLE ROW LEVEL SECURITY;

-- Create policies for accommodations table
-- Admin users can do everything
CREATE POLICY "Admin users can manage accommodations" ON public.accommodations
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Staff users can view all accommodations
CREATE POLICY "Staff users can view accommodations" ON public.accommodations
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('admin', 'staff')
        )
    );

-- Public users can only view published accommodations
CREATE POLICY "Public users can view published accommodations" ON public.accommodations
    FOR SELECT USING (status = 'published');

-- Create function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_accommodations_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    NEW.updated_by = auth.uid();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
CREATE TRIGGER trigger_accommodations_updated_at
    BEFORE UPDATE ON public.accommodations
    FOR EACH ROW
    EXECUTE FUNCTION update_accommodations_updated_at();

-- Create function to set created_by on insert
CREATE OR REPLACE FUNCTION set_accommodations_created_by()
RETURNS TRIGGER AS $$
BEGIN
    NEW.created_by = auth.uid();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically set created_by
CREATE TRIGGER trigger_accommodations_created_by
    BEFORE INSERT ON public.accommodations
    FOR EACH ROW
    EXECUTE FUNCTION set_accommodations_created_by();

-- Grant permissions
GRANT ALL ON public.accommodations TO authenticated;
GRANT SELECT ON public.accommodations TO anon;

-- Create storage bucket for accommodation images
INSERT INTO storage.buckets (id, name, public)
VALUES ('accommodation-images', 'accommodation-images', true)
ON CONFLICT (id) DO NOTHING;

-- Create storage policies for accommodation images
-- Admin users can upload, update, and delete images
CREATE POLICY "Admin users can manage accommodation images" ON storage.objects
    FOR ALL USING (
        bucket_id = 'accommodation-images' AND
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Public users can view images
CREATE POLICY "Public users can view accommodation images" ON storage.objects
    FOR SELECT USING (bucket_id = 'accommodation-images');

-- Add some sample data for testing
INSERT INTO public.accommodations (
    name, type, description, special_features, amenities, 
    price_range, capacity, status, featured
) VALUES 
(
    'Luxury Safari Suite',
    'suite',
    'Spacious suite with panoramic views of the Selous wilderness, featuring a private deck and luxury amenities.',
    'Ideal for honeymooners, private butler service available',
    '["Private balcony", "Minibar", "Air conditioning", "Heated shower", "King-size bed"]'::jsonb,
    '$450–$650',
    2,
    'published',
    true
),
(
    'Traditional Banda',
    'banda',
    'Authentic African banda with modern comforts, built using traditional materials and techniques.',
    'Eco-friendly construction, solar-powered',
    '["Private bathroom", "Mosquito netting", "Fan", "Hot water", "Twin beds"]'::jsonb,
    '$280–$380',
    4,
    'published',
    false
),
(
    'Riverside Camping',
    'campsite',
    'Premium camping experience by the river with full amenities and guided activities.',
    'Perfect for adventure seekers, includes camping equipment',
    '["Shared bathroom", "Hot showers", "Campfire area", "Guided walks", "Meals included"]'::jsonb,
    '$120–$180',
    6,
    'draft',
    false
);

-- Create view for public accommodation data (only published)
CREATE OR REPLACE VIEW public.accommodations_public AS
SELECT 
    id,
    name,
    type,
    description,
    special_features,
    amenities,
    price_range,
    capacity,
    images,
    featured,
    created_at
FROM public.accommodations
WHERE status = 'published'
ORDER BY featured DESC, created_at DESC;

-- Grant access to the public view
GRANT SELECT ON public.accommodations_public TO anon;
GRANT SELECT ON public.accommodations_public TO authenticated;

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Accommodations table and storage bucket created successfully!';
    RAISE NOTICE 'Sample data inserted for testing.';
    RAISE NOTICE 'Storage bucket "accommodation-images" is ready for file uploads.';
END $$;
