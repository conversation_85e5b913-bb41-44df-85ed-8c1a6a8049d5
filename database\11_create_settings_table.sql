-- =====================================================
-- Malombo Admin Panel - Settings Table Setup
-- =====================================================
-- This script creates the settings table for system configuration

-- Drop existing table if it exists (for development)
DROP TABLE IF EXISTS public.settings CASCADE;

-- Create settings table with key-value JSON storage
CREATE TABLE public.settings (
    -- Primary key
    key TEXT PRIMARY KEY,
    
    -- JSON value for flexible configuration storage
    value JSONB NOT NULL,
    
    -- Metadata
    description TEXT,
    category TEXT NOT NULL DEFAULT 'general',
    is_public BOOLEAN NOT NULL DEFAULT false, -- Whether setting can be accessed by public
    
    -- Audit trail
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID REFERENCES auth.users(id)
);

-- Create indexes
CREATE INDEX idx_settings_category ON public.settings(category);
CREATE INDEX idx_settings_public ON public.settings(is_public);
CREATE INDEX idx_settings_updated_at ON public.settings(updated_at);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_settings_updated_at
    BEFORE UPDATE ON public.settings
    FOR EACH ROW
    EXECUTE FUNCTION update_settings_updated_at();

-- Enable Row Level Security
ALTER TABLE public.settings ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Admin users can manage all settings
CREATE POLICY "Admin users can manage all settings" ON public.settings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
            AND profiles.is_active = true
        )
    );

-- Staff users can view settings but not modify
CREATE POLICY "Staff users can view settings" ON public.settings
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('admin', 'staff')
            AND profiles.is_active = true
        )
    );

-- Public users can view public settings only
CREATE POLICY "Public users can view public settings" ON public.settings
    FOR SELECT USING (is_public = true);

-- Insert default settings
INSERT INTO public.settings (key, value, description, category, is_public) VALUES

-- Lodge Contacts
('lodge_contacts', '{
    "emails": {
        "reservations": "<EMAIL>",
        "sales": "<EMAIL>",
        "info": "<EMAIL>",
        "manager": "<EMAIL>"
    },
    "phones": [
        "+255 759 511 901",
        "+255 682 833 104",
        "+254 781 663 555"
    ],
    "address": {
        "street": "Selous Game Reserve",
        "city": "Morogoro",
        "country": "Tanzania",
        "postal_code": ""
    }
}', 'Lodge contact information including emails, phones, and address', 'contacts', true),

-- Policies
('check_in_policy', '{
    "check_in_time": "12:00",
    "check_in_end": "18:00",
    "check_out_time": "10:00",
    "late_checkout_fee": 50,
    "early_checkin_available": true,
    "early_checkin_fee": 30
}', 'Check-in and check-out time policies', 'policies', true),

('child_pricing_policy', '{
    "age_groups": {
        "infant": {
            "min_age": 0,
            "max_age": 4,
            "price_percentage": 0,
            "description": "Children 0-4 years stay free"
        },
        "child": {
            "min_age": 5,
            "max_age": 13,
            "price_percentage": 50,
            "description": "Children 5-13 years pay 50% of adult rate"
        },
        "adult": {
            "min_age": 14,
            "max_age": null,
            "price_percentage": 100,
            "description": "14+ years pay full adult rate"
        }
    },
    "max_children_per_room": 3,
    "infant_bed_required": false
}', 'Child pricing and accommodation policies', 'policies', true),

('cancellation_policy', '{
    "free_cancellation_days": 14,
    "partial_refund_days": 7,
    "partial_refund_percentage": 50,
    "no_refund_days": 3,
    "emergency_exceptions": true,
    "policy_text": "Free cancellation up to 14 days before arrival. 50% refund for cancellations 7-14 days before. No refund for cancellations within 7 days of arrival."
}', 'Booking cancellation policies', 'policies', true),

-- Pricing Configuration
('pricing_defaults', '{
    "default_currency": "USD",
    "accepted_currencies": ["USD", "EUR", "GBP", "TZS"],
    "tax_rate": 18,
    "service_charge": 0,
    "seasonal_pricing": true,
    "discount_presets": [
        {"name": "Early Bird", "percentage": 10, "description": "Book 60+ days in advance"},
        {"name": "Extended Stay", "percentage": 15, "description": "Stay 7+ nights"},
        {"name": "Repeat Guest", "percentage": 5, "description": "Previous guest discount"},
        {"name": "Group Booking", "percentage": 12, "description": "4+ rooms booked together"}
    ]
}', 'Default pricing configuration and discount presets', 'pricing', false),

-- Email Configuration
('email_settings', '{
    "sender_name": "Malombo Selous Forest Camp",
    "sender_email": "<EMAIL>",
    "reply_to": "<EMAIL>",
    "default_cc": ["<EMAIL>"],
    "default_bcc": [],
    "booking_notifications": {
        "new_booking": ["<EMAIL>", "<EMAIL>"],
        "booking_confirmed": ["<EMAIL>"],
        "booking_cancelled": ["<EMAIL>", "<EMAIL>"],
        "payment_received": ["<EMAIL>"]
    }
}', 'Email configuration and notification recipients', 'email', false),

-- Email Templates
('email_templates', '{
    "booking_confirmation": {
        "subject": "Booking Confirmation - Malombo Selous Forest Camp",
        "html_body": "<!DOCTYPE html><html><head><title>Booking Confirmation</title></head><body><h1>Thank you for your booking!</h1><p>Dear {{guest_name}},</p><p>We are delighted to confirm your reservation at Malombo Selous Forest Camp.</p><h2>Booking Details</h2><ul><li><strong>Booking Reference:</strong> {{booking_id}}</li><li><strong>Check-in:</strong> {{check_in_date}}</li><li><strong>Check-out:</strong> {{check_out_date}}</li><li><strong>Accommodation:</strong> {{accommodation_name}}</li><li><strong>Guests:</strong> {{guest_count}}</li><li><strong>Total Amount:</strong> {{total_amount}} {{currency}}</li></ul><p>We look forward to welcoming you to our camp!</p><p>Best regards,<br>The Malombo Team</p></body></html>"
    },
    "booking_update": {
        "subject": "Booking Update - Malombo Selous Forest Camp",
        "html_body": "<!DOCTYPE html><html><head><title>Booking Update</title></head><body><h1>Booking Update</h1><p>Dear {{guest_name}},</p><p>Your booking has been updated.</p><h2>Updated Details</h2><ul><li><strong>Booking Reference:</strong> {{booking_id}}</li><li><strong>Status:</strong> {{status}}</li><li><strong>Payment Status:</strong> {{payment_status}}</li></ul><p>If you have any questions, please contact us.</p><p>Best regards,<br>The Malombo Team</p></body></html>"
    },
    "booking_cancellation": {
        "subject": "Booking Cancellation - Malombo Selous Forest Camp",
        "html_body": "<!DOCTYPE html><html><head><title>Booking Cancellation</title></head><body><h1>Booking Cancellation</h1><p>Dear {{guest_name}},</p><p>We regret to inform you that your booking has been cancelled.</p><h2>Cancelled Booking Details</h2><ul><li><strong>Booking Reference:</strong> {{booking_id}}</li><li><strong>Original Check-in:</strong> {{check_in_date}}</li><li><strong>Cancellation Date:</strong> {{cancellation_date}}</li></ul><p>We hope to welcome you in the future.</p><p>Best regards,<br>The Malombo Team</p></body></html>"
    }
}', 'Email templates for booking notifications', 'email', false),

-- Display Settings
('display_settings', '{
    "theme": {
        "primary_color": "#d97706",
        "secondary_color": "#92400e",
        "accent_color": "#f59e0b",
        "background_color": "#fef3c7",
        "text_color": "#1f2937"
    },
    "logo_url": "/images/malombo-logo.png",
    "favicon_url": "/images/favicon.ico",
    "default_images": {
        "accommodation_placeholder": "/images/accommodation-placeholder.jpg",
        "activity_placeholder": "/images/activity-placeholder.jpg"
    },
    "date_format": "YYYY-MM-DD",
    "time_format": "HH:mm",
    "currency_format": {
        "symbol_position": "before",
        "decimal_places": 2,
        "thousands_separator": ","
    }
}', 'Display and theme configuration', 'display', true),

-- Business Information
('business_info', '{
    "name": "Malombo Selous Forest Camp",
    "tagline": "Authentic Safari Experience in the Heart of Selous",
    "description": "Experience the untamed beauty of Tanzania''s largest game reserve at our intimate safari camp.",
    "established_year": 2010,
    "license_number": "TZ-SAFARI-2010-001",
    "social_media": {
        "facebook": "https://facebook.com/malombocamp",
        "instagram": "https://instagram.com/malombocamp",
        "twitter": "https://twitter.com/malombocamp"
    },
    "certifications": [
        "Tanzania Tourism Association",
        "Sustainable Tourism Certification",
        "Wildlife Conservation Supporter"
    ]
}', 'Business information and certifications', 'business', true);

-- Create helper function to get settings by category
CREATE OR REPLACE FUNCTION get_settings_by_category(category_name TEXT)
RETURNS TABLE (
    key TEXT,
    value JSONB,
    description TEXT,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT s.key, s.value, s.description, s.updated_at
    FROM public.settings s
    WHERE s.category = category_name
    ORDER BY s.key;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create helper function to update setting
CREATE OR REPLACE FUNCTION update_setting(
    setting_key TEXT,
    setting_value JSONB,
    user_id UUID DEFAULT NULL
)
RETURNS BOOLEAN AS $$
BEGIN
    -- Check if user is admin
    IF NOT EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = COALESCE(user_id, auth.uid())
        AND profiles.role = 'admin'
        AND profiles.is_active = true
    ) THEN
        RAISE EXCEPTION 'Access denied: Admin role required';
    END IF;

    -- Update or insert setting
    INSERT INTO public.settings (key, value, updated_by)
    VALUES (setting_key, setting_value, COALESCE(user_id, auth.uid()))
    ON CONFLICT (key) 
    DO UPDATE SET 
        value = EXCLUDED.value,
        updated_by = EXCLUDED.updated_by,
        updated_at = NOW();

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_settings_by_category(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION update_setting(TEXT, JSONB, UUID) TO authenticated;

COMMENT ON TABLE public.settings IS 'System configuration settings with JSON values';
COMMENT ON FUNCTION get_settings_by_category IS 'Get all settings for a specific category';
COMMENT ON FUNCTION update_setting IS 'Update a setting value (admin only)';
