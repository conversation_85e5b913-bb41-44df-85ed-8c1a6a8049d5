{"version": 2, "buildCommand": "npm run build", "outputDirectory": "dist", "installCommand": "npm install", "framework": "vite", "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/assets/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/images/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*\\.(js|css|ico|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "functions": {"app/api/**/*.js": {"runtime": "nodejs18.x"}}, "regions": ["iad1"], "env": {"NODE_ENV": "production"}}