// =====================================================
// Public Accommodations API
// =====================================================
// Service functions for public-facing accommodation queries
// Only returns published accommodations with proper data formatting

import { supabase } from './supabase';
import type { Accommodation } from '@/types/accommodation';

// =====================================================
// Public Accommodation Queries
// =====================================================

/**
 * Get all published accommodations for public display
 * Sorted by featured status first, then by creation date
 */
export async function getPublishedAccommodations(): Promise<Accommodation[]> {
  try {
    const { data, error } = await supabase
      .from('accommodations_public')
      .select('*')
      .order('featured', { ascending: false })
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching published accommodations:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getPublishedAccommodations:', error);
    return [];
  }
}

/**
 * Get featured accommodations for homepage display
 * Limited to 6 most recent featured accommodations
 */
export async function getFeaturedAccommodations(limit: number = 6): Promise<Accommodation[]> {
  try {
    const { data, error } = await supabase
      .from('accommodations_public')
      .select('*')
      .eq('featured', true)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching featured accommodations:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getFeaturedAccommodations:', error);
    return [];
  }
}

/**
 * Get accommodations by type for category pages
 */
export async function getAccommodationsByType(type: string): Promise<Accommodation[]> {
  try {
    const { data, error } = await supabase
      .from('accommodations_public')
      .select('*')
      .eq('type', type)
      .order('featured', { ascending: false })
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching accommodations by type:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getAccommodationsByType:', error);
    return [];
  }
}

/**
 * Get a single published accommodation by ID
 */
export async function getPublishedAccommodation(id: string): Promise<Accommodation | null> {
  try {
    const { data, error } = await supabase
      .from('accommodations_public')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('Error fetching published accommodation:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in getPublishedAccommodation:', error);
    return null;
  }
}

/**
 * Search published accommodations by name or description
 */
export async function searchPublishedAccommodations(
  searchTerm: string,
  limit: number = 20
): Promise<Accommodation[]> {
  try {
    const { data, error } = await supabase
      .from('accommodations_public')
      .select('*')
      .or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`)
      .order('featured', { ascending: false })
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error searching accommodations:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in searchPublishedAccommodations:', error);
    return [];
  }
}

/**
 * Get accommodations with specific amenities
 */
export async function getAccommodationsByAmenities(
  amenities: string[],
  limit: number = 20
): Promise<Accommodation[]> {
  try {
    // Build the query to check if any of the amenities exist in the amenities array
    let query = supabase
      .from('accommodations_public')
      .select('*');

    // Add amenity filters
    amenities.forEach(amenity => {
      query = query.contains('amenities', [amenity]);
    });

    const { data, error } = await query
      .order('featured', { ascending: false })
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching accommodations by amenities:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getAccommodationsByAmenities:', error);
    return [];
  }
}

/**
 * Get accommodations by capacity range
 */
export async function getAccommodationsByCapacity(
  minCapacity: number,
  maxCapacity?: number
): Promise<Accommodation[]> {
  try {
    let query = supabase
      .from('accommodations_public')
      .select('*')
      .gte('capacity', minCapacity);

    if (maxCapacity) {
      query = query.lte('capacity', maxCapacity);
    }

    const { data, error } = await query
      .order('featured', { ascending: false })
      .order('capacity', { ascending: true });

    if (error) {
      console.error('Error fetching accommodations by capacity:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getAccommodationsByCapacity:', error);
    return [];
  }
}

// =====================================================
// Data Transformation Utilities
// =====================================================

/**
 * Transform accommodation data for public display
 * Ensures consistent formatting and removes admin-only fields
 */
export function transformAccommodationForPublic(accommodation: Accommodation): Accommodation {
  return {
    id: accommodation.id,
    name: accommodation.name,
    type: accommodation.type,
    description: accommodation.description,
    special_features: accommodation.special_features,
    amenities: accommodation.amenities || [],
    price_range: accommodation.price_range,
    capacity: accommodation.capacity,
    images: accommodation.images || [],
    status: accommodation.status,
    featured: accommodation.featured,
    created_at: accommodation.created_at,
    updated_at: accommodation.updated_at,
    // Exclude admin fields
    created_by: undefined,
    updated_by: undefined
  };
}

/**
 * Group accommodations by type for category display
 */
export function groupAccommodationsByType(accommodations: Accommodation[]): Record<string, Accommodation[]> {
  return accommodations.reduce((groups, accommodation) => {
    const type = accommodation.type;
    if (!groups[type]) {
      groups[type] = [];
    }
    groups[type].push(accommodation);
    return groups;
  }, {} as Record<string, Accommodation[]>);
}

/**
 * Get unique amenities from a list of accommodations
 */
export function getUniqueAmenities(accommodations: Accommodation[]): string[] {
  const amenitiesSet = new Set<string>();
  
  accommodations.forEach(accommodation => {
    if (accommodation.amenities) {
      accommodation.amenities.forEach(amenity => {
        amenitiesSet.add(amenity);
      });
    }
  });

  return Array.from(amenitiesSet).sort();
}

/**
 * Get accommodation types with counts
 */
export function getAccommodationTypeCounts(accommodations: Accommodation[]): Record<string, number> {
  return accommodations.reduce((counts, accommodation) => {
    const type = accommodation.type;
    counts[type] = (counts[type] || 0) + 1;
    return counts;
  }, {} as Record<string, number>);
}

/**
 * Filter accommodations by price range
 * Assumes price_range format like "$180–$380" or "$180-$380"
 */
export function filterAccommodationsByPriceRange(
  accommodations: Accommodation[],
  minPrice: number,
  maxPrice: number
): Accommodation[] {
  return accommodations.filter(accommodation => {
    const priceRange = accommodation.price_range;
    
    // Extract numbers from price range string
    const priceNumbers = priceRange.match(/\d+/g);
    if (!priceNumbers || priceNumbers.length === 0) {
      return false;
    }

    const lowestPrice = parseInt(priceNumbers[0]);
    const highestPrice = priceNumbers.length > 1 ? parseInt(priceNumbers[1]) : lowestPrice;

    // Check if the accommodation's price range overlaps with the filter range
    return lowestPrice <= maxPrice && highestPrice >= minPrice;
  });
}

/**
 * Sort accommodations by various criteria
 */
export function sortAccommodations(
  accommodations: Accommodation[],
  sortBy: 'name' | 'price' | 'capacity' | 'featured' | 'newest'
): Accommodation[] {
  const sorted = [...accommodations];

  switch (sortBy) {
    case 'name':
      return sorted.sort((a, b) => a.name.localeCompare(b.name));
    
    case 'price':
      return sorted.sort((a, b) => {
        const aPrice = parseInt(a.price_range.match(/\d+/)?.[0] || '0');
        const bPrice = parseInt(b.price_range.match(/\d+/)?.[0] || '0');
        return aPrice - bPrice;
      });
    
    case 'capacity':
      return sorted.sort((a, b) => a.capacity - b.capacity);
    
    case 'featured':
      return sorted.sort((a, b) => {
        if (a.featured && !b.featured) return -1;
        if (!a.featured && b.featured) return 1;
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      });
    
    case 'newest':
    default:
      return sorted.sort((a, b) => 
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      );
  }
}
