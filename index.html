<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="Luxury safari lodge in Tanzania's Nyerere National Park offering authentic wilderness experiences, luxury accommodations, and unforgettable African adventures."
    />
    <meta
      name="keywords"
      content="safari, Tanzania, Nyerere National Park, Selous, luxury lodge, wildlife, accommodation, booking"
    />
    <meta name="author" content="Malombo Selous Forest Camp" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta
      property="og:title"
      content="Malombo Selous Forest Camp - Luxury Safari Lodge Tanzania"
    />
    <meta
      property="og:description"
      content="Experience the ultimate African safari adventure at our luxury lodge in Tanzania's Nyerere National Park."
    />
    <meta property="og:image" content="/images/camp-hero.jpg" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta
      property="twitter:title"
      content="Malombo Selous Forest Camp - Luxury Safari Lodge Tanzania"
    />
    <meta
      property="twitter:description"
      content="Experience the ultimate African safari adventure at our luxury lodge in Tanzania's Nyerere National Park."
    />
    <meta property="twitter:image" content="/images/camp-hero.jpg" />

    <title>Malombo Selous Forest Camp</title>

    <!-- GitHub Pages SPA routing script -->
    <script type="text/javascript">
      // Single Page Apps for GitHub Pages
      // https://github.com/rafgraph/spa-github-pages
      (function (l) {
        if (l.search[1] === "/") {
          var decoded = l.search
            .slice(1)
            .split("&")
            .map(function (s) {
              return s.replace(/~and~/g, "&");
            })
            .join("?");
          window.history.replaceState(
            null,
            null,
            l.pathname.slice(0, -1) + decoded + l.hash
          );
        }
      })(window.location);
    </script>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
