// =====================================================
// Bookings Service Layer
// =====================================================
// Service functions for booking CRUD operations and management

import { supabase } from "./supabase";
import type {
  AdminBooking,
  BookingWithDetails,
  BookingCreateRequest,
  BookingUpdateRequest,
  BookingFilters,
  BookingSort,
  BookingListResponse,
  BookingResponse,
  BookingStatistics,
  BookingCSVExportConfig,
  BookingStatus,
  PaymentStatus,
  PricingCalculationRequest,
  PricingCalculationResult,
  AvailabilityCheck,
  AvailabilityResult,
} from "@/types/booking";

// =====================================================
// CRUD Operations
// =====================================================

/**
 * Get bookings with filtering, sorting, and pagination
 */
export async function getBookings(
  filters: BookingFilters,
  sort: BookingSort,
  pagination: { page: number; limit: number }
): Promise<BookingListResponse> {
  try {
    const { data, error } = await supabase.rpc("get_bookings_filtered", {
      search_term: filters.search || null,
      status_filter: filters.status === "all" ? null : filters.status,
      payment_filter:
        filters.payment_status === "all" ? null : filters.payment_status,
      accommodation_filter: filters.accommodation_id || null,
      source_filter: filters.source === "all" ? null : filters.source,
      date_from: filters.date_from || null,
      date_to: filters.date_to || null,
      sort_by: sort.field,
      sort_order: sort.order,
      page_limit: pagination.limit,
      page_offset: (pagination.page - 1) * pagination.limit,
    });

    if (error) throw error;

    const totalCount = data?.[0]?.total_count || 0;
    const bookings =
      data?.map((row: any) => ({
        id: row.id,
        guest_full_name: row.guest_full_name,
        email: row.email,
        phone: row.phone,
        country: row.country,
        adults: row.adults,
        children_5_13: row.children_5_13,
        children_0_4: row.children_0_4,
        accommodation: {
          id: row.accommodation_id,
          name: row.accommodation_name,
          type: row.accommodation_type,
          capacity: 0, // Will be populated from accommodation details if needed
          price_range: "",
        },
        check_in: row.check_in,
        check_out: row.check_out,
        nights: row.nights,
        currency: row.currency,
        total_amount: row.total_amount,
        status: row.status,
        payment_status: row.payment_status,
        source: row.source,
        created_at: row.created_at,
        // Additional fields will be populated by getBookingDetails if needed
        activity_ids: [],
        activities: [],
        base_amount: 0,
        discounts: 0,
        taxes: 0,
        payment_method: "",
        payment_reference: "",
        notes_admin: "",
        notes_guest: "",
        status_history: [],
        updated_at: "",
        created_by: "",
        updated_by: "",
      })) || [];

    return {
      data: bookings,
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total: totalCount,
        totalPages: Math.ceil(totalCount / pagination.limit),
      },
      success: true,
    };
  } catch (error) {
    console.error("Error fetching bookings:", error);
    return {
      data: [],
      pagination: {
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0,
      },
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to fetch bookings",
    };
  }
}

/**
 * Get a single booking by ID with full details
 */
export async function getBooking(id: string): Promise<BookingResponse> {
  try {
    const { data, error } = await supabase.rpc("get_booking_details", {
      booking_id: id,
    });

    if (error) throw error;
    if (!data || data.length === 0) {
      throw new Error("Booking not found");
    }

    const result = data[0];
    const booking: AdminBooking = {
      ...result.booking_data,
      accommodation: result.accommodation_data,
      activities: result.activities_data || [],
    };

    return {
      data: booking,
      success: true,
    };
  } catch (error) {
    console.error("Error fetching booking:", error);
    return {
      data: null as any,
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to fetch booking",
    };
  }
}

/**
 * Create a new booking
 */
export async function createBooking(
  bookingData: BookingCreateRequest
): Promise<BookingResponse> {
  try {
    // Calculate total amount if not provided
    if (!bookingData.total_amount) {
      const pricingResult = await calculateBookingPricing({
        accommodation_id: bookingData.accommodation_id,
        check_in: bookingData.check_in,
        check_out: bookingData.check_out,
        party: {
          adults: bookingData.adults,
          children_5_13: bookingData.children_5_13,
          children_0_4: bookingData.children_0_4,
        },
        activity_ids: bookingData.activity_ids || [],
        currency: bookingData.currency,
      });

      if (pricingResult.success) {
        bookingData.base_amount = pricingResult.data.subtotal;
        bookingData.taxes = pricingResult.data.taxes;
        bookingData.total_amount = pricingResult.data.total;
      }
    }

    const { data, error } = await supabase
      .from("bookings")
      .insert([bookingData])
      .select()
      .single();

    if (error) throw error;

    return {
      data,
      success: true,
      message: "Booking created successfully",
    };
  } catch (error) {
    console.error("Error creating booking:", error);
    return {
      data: null as any,
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to create booking",
    };
  }
}

/**
 * Update an existing booking
 */
export async function updateBooking(
  bookingData: BookingUpdateRequest
): Promise<BookingResponse> {
  try {
    const { id, ...updateData } = bookingData;

    // Recalculate pricing if relevant fields changed
    if (
      updateData.accommodation_id ||
      updateData.check_in ||
      updateData.check_out ||
      updateData.adults ||
      updateData.children_5_13 ||
      updateData.children_0_4 ||
      updateData.activity_ids
    ) {
      // Get current booking data to fill in missing fields
      const currentBooking = await getBooking(id);
      if (currentBooking.success) {
        const pricingRequest = {
          accommodation_id:
            updateData.accommodation_id || currentBooking.data.accommodation_id,
          check_in: updateData.check_in || currentBooking.data.check_in,
          check_out: updateData.check_out || currentBooking.data.check_out,
          party: {
            adults: updateData.adults || currentBooking.data.adults,
            children_5_13:
              updateData.children_5_13 || currentBooking.data.children_5_13,
            children_0_4:
              updateData.children_0_4 || currentBooking.data.children_0_4,
          },
          activity_ids:
            updateData.activity_ids || currentBooking.data.activity_ids,
          currency: updateData.currency || currentBooking.data.currency,
        };

        const pricingResult = await calculateBookingPricing(pricingRequest);
        if (pricingResult.success) {
          updateData.base_amount = pricingResult.data.subtotal;
          updateData.taxes = pricingResult.data.taxes;
          updateData.total_amount = pricingResult.data.total;
        }
      }
    }

    const { data, error } = await supabase
      .from("bookings")
      .update(updateData)
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;

    return {
      data,
      success: true,
      message: "Booking updated successfully",
    };
  } catch (error) {
    console.error("Error updating booking:", error);
    return {
      data: null as any,
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to update booking",
    };
  }
}

/**
 * Delete a booking
 */
export async function deleteBooking(
  id: string
): Promise<{ success: boolean; message: string }> {
  try {
    const { error } = await supabase.from("bookings").delete().eq("id", id);

    if (error) throw error;

    return {
      success: true,
      message: "Booking deleted successfully",
    };
  } catch (error) {
    console.error("Error deleting booking:", error);
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to delete booking",
    };
  }
}

// =====================================================
// Status and Payment Management
// =====================================================

/**
 * Update booking status
 */
export async function updateBookingStatus(
  id: string,
  status: BookingStatus,
  adminNotes?: string
): Promise<{ success: boolean; message: string }> {
  try {
    const { data, error } = await supabase.rpc("update_booking_status", {
      booking_id: id,
      new_status: status,
      admin_notes: adminNotes || null,
    });

    if (error) throw error;

    return {
      success: true,
      message: `Booking status updated to ${status}`,
    };
  } catch (error) {
    console.error("Error updating booking status:", error);
    return {
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to update booking status",
    };
  }
}

/**
 * Update payment status
 */
export async function updatePaymentStatus(
  id: string,
  paymentStatus: PaymentStatus,
  paymentMethod?: string,
  paymentReference?: string
): Promise<{ success: boolean; message: string }> {
  try {
    const { data, error } = await supabase.rpc("update_payment_status", {
      booking_id: id,
      new_payment_status: paymentStatus,
      payment_method_val: paymentMethod || null,
      payment_ref: paymentReference || null,
    });

    if (error) throw error;

    return {
      success: true,
      message: `Payment status updated to ${paymentStatus}`,
    };
  } catch (error) {
    console.error("Error updating payment status:", error);
    return {
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to update payment status",
    };
  }
}

// =====================================================
// Statistics and Analytics
// =====================================================

/**
 * Get booking statistics
 */
export async function getBookingStatistics(
  dateFrom?: string,
  dateTo?: string
): Promise<{ data: BookingStatistics; success: boolean; message?: string }> {
  try {
    const { data, error } = await supabase.rpc("get_booking_statistics", {
      date_from: dateFrom || null,
      date_to: dateTo || null,
    });

    if (error) throw error;

    const stats = data?.[0] || {
      total_bookings: 0,
      confirmed_bookings: 0,
      pending_bookings: 0,
      cancelled_bookings: 0,
      completed_bookings: 0,
      total_revenue: 0,
      paid_revenue: 0,
      pending_revenue: 0,
      average_booking_value: 0,
      total_guests: 0,
    };

    return {
      data: stats,
      success: true,
    };
  } catch (error) {
    console.error("Error fetching booking statistics:", error);
    return {
      data: {
        total_bookings: 0,
        confirmed_bookings: 0,
        pending_bookings: 0,
        cancelled_bookings: 0,
        completed_bookings: 0,
        total_revenue: 0,
        paid_revenue: 0,
        pending_revenue: 0,
        average_booking_value: 0,
        total_guests: 0,
      },
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to fetch statistics",
    };
  }
}

// =====================================================
// Pricing and Availability
// =====================================================

/**
 * Calculate booking pricing
 */
export async function calculateBookingPricing(
  request: PricingCalculationRequest
): Promise<{
  data: PricingCalculationResult;
  success: boolean;
  message?: string;
}> {
  try {
    const { data, error } = await supabase.rpc("calculate_booking_totals", {
      base_amount: request.accommodation_id ? 100 : 0, // This would be calculated based on accommodation and dates
      currency_code: request.currency,
    });

    if (error) throw error;

    const result = data?.[0] || {
      base_amount_out: 0,
      tax_amount: 0,
      service_charge: 0,
      total_amount: 0,
      tax_rate: 0,
      service_rate: 0,
    };

    // This is a simplified calculation - in a real implementation,
    // you would calculate based on accommodation rates, activity costs, etc.
    const pricingResult: PricingCalculationResult = {
      accommodation_cost: result.base_amount_out,
      activities_cost: 0, // Would be calculated from activity_ids
      subtotal: result.base_amount_out,
      discounts: 0,
      taxes: result.tax_amount,
      service_charges: result.service_charge,
      total: result.total_amount,
      breakdown: [
        {
          item: "Accommodation",
          quantity: 1,
          unit_price: result.base_amount_out,
          total_price: result.base_amount_out,
        },
      ],
    };

    return {
      data: pricingResult,
      success: true,
    };
  } catch (error) {
    console.error("Error calculating pricing:", error);
    return {
      data: {
        accommodation_cost: 0,
        activities_cost: 0,
        subtotal: 0,
        discounts: 0,
        taxes: 0,
        service_charges: 0,
        total: 0,
        breakdown: [],
      },
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to calculate pricing",
    };
  }
}

/**
 * Check accommodation availability
 */
export async function checkAvailability(
  request: AvailabilityCheck
): Promise<{ data: AvailabilityResult; success: boolean; message?: string }> {
  try {
    // Check for conflicting bookings
    const { data: conflicts, error } = await supabase
      .from("bookings")
      .select("id, guest_full_name, check_in, check_out")
      .eq("accommodation_id", request.accommodation_id)
      .neq("status", "cancelled")
      .or(
        `check_in.lte.${request.check_out},check_out.gte.${request.check_in}`
      );

    if (error) throw error;

    const available = !conflicts || conflicts.length === 0;

    return {
      data: {
        available,
        conflicts:
          conflicts?.map((conflict) => ({
            booking_id: conflict.id,
            guest_name: conflict.guest_full_name,
            check_in: conflict.check_in,
            check_out: conflict.check_out,
          })) || [],
        suggestions: available
          ? []
          : [
              {
                check_in: request.check_in,
                check_out: request.check_out,
                reason: "Consider alternative dates or accommodations",
              },
            ],
      },
      success: true,
    };
  } catch (error) {
    console.error("Error checking availability:", error);
    return {
      data: {
        available: false,
        conflicts: [],
        suggestions: [],
      },
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to check availability",
    };
  }
}

// =====================================================
// CSV Export
// =====================================================

/**
 * Export bookings to CSV
 */
export async function exportBookingsToCSV(
  config: BookingCSVExportConfig
): Promise<{ data: string; success: boolean; message?: string }> {
  try {
    // Get bookings based on filters
    const filters = config.filters || {};
    const sort: BookingSort = { field: "check_in", order: "desc" };

    // Get all bookings (no pagination for export)
    const response = await getBookings(filters, sort, {
      page: 1,
      limit: 10000,
    });

    if (!response.success) {
      throw new Error(response.message || "Failed to fetch bookings");
    }

    const bookings = response.data;

    // Define available columns
    const availableColumns = {
      booking_id: "Booking ID",
      guest_name: "Guest Name",
      email: "Email",
      phone: "Phone",
      country: "Country",
      adults: "Adults",
      children_5_13: "Children (5-13)",
      children_0_4: "Children (0-4)",
      accommodation: "Accommodation",
      check_in: "Check In",
      check_out: "Check Out",
      nights: "Nights",
      activities: "Activities",
      currency: "Currency",
      base_amount: "Base Amount",
      discounts: "Discounts",
      taxes: "Taxes",
      total_amount: "Total Amount",
      status: "Status",
      payment_status: "Payment Status",
      payment_method: "Payment Method",
      source: "Source",
      created_at: "Created At",
      notes_guest: "Guest Notes",
    };

    // Use specified columns or default to all
    const columnsToExport =
      config.columns.length > 0
        ? config.columns
        : Object.keys(availableColumns);

    // Create CSV header
    const headers = columnsToExport.map(
      (col) => availableColumns[col as keyof typeof availableColumns] || col
    );

    // Create CSV rows
    const rows = bookings.map((booking) => {
      return columnsToExport.map((column) => {
        switch (column) {
          case "booking_id":
            return booking.id;
          case "guest_name":
            return booking.guest_full_name;
          case "email":
            return booking.email;
          case "phone":
            return booking.phone;
          case "country":
            return booking.country;
          case "adults":
            return booking.adults;
          case "children_5_13":
            return booking.children_5_13;
          case "children_0_4":
            return booking.children_0_4;
          case "accommodation":
            return booking.accommodation?.name || "";
          case "check_in":
            return booking.check_in;
          case "check_out":
            return booking.check_out;
          case "nights":
            return booking.nights;
          case "activities":
            return booking.activities?.map((a) => a.title).join("; ") || "";
          case "currency":
            return booking.currency;
          case "base_amount":
            return booking.base_amount;
          case "discounts":
            return booking.discounts;
          case "taxes":
            return booking.taxes;
          case "total_amount":
            return booking.total_amount;
          case "status":
            return booking.status;
          case "payment_status":
            return booking.payment_status;
          case "payment_method":
            return booking.payment_method || "";
          case "source":
            return booking.source;
          case "created_at":
            return new Date(booking.created_at).toISOString().split("T")[0];
          case "notes_guest":
            return booking.notes_guest || "";
          default:
            return "";
        }
      });
    });

    // Convert to CSV format
    const csvContent = [
      headers.join(","),
      ...rows.map((row) =>
        row
          .map((cell) => {
            // Escape quotes and wrap in quotes if contains comma, quote, or newline
            const cellStr = String(cell || "");
            if (
              cellStr.includes(",") ||
              cellStr.includes('"') ||
              cellStr.includes("\n")
            ) {
              return `"${cellStr.replace(/"/g, '""')}"`;
            }
            return cellStr;
          })
          .join(",")
      ),
    ].join("\n");

    // Add BOM for UTF-8 encoding
    const csvWithBOM = "\uFEFF" + csvContent;

    return {
      data: csvWithBOM,
      success: true,
      message: `Exported ${bookings.length} bookings to CSV`,
    };
  } catch (error) {
    console.error("Error exporting bookings to CSV:", error);
    return {
      data: "",
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to export bookings",
    };
  }
}

/**
 * Download CSV file
 */
export function downloadCSV(
  csvData: string,
  filename: string = "bookings-export.csv"
): void {
  try {
    const blob = new Blob([csvData], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", filename);
      link.style.visibility = "hidden";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }
  } catch (error) {
    console.error("Error downloading CSV:", error);
    throw new Error("Failed to download CSV file");
  }
}
