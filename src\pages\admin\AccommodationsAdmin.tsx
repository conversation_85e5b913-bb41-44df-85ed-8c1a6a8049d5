import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Star,
  StarOff,
  Eye,
  EyeOff,
  ArrowUpDown,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { toast } from "sonner";

import {
  getAccommodations,
  deleteAccommodation,
  toggleAccommodationFeatured,
  updateAccommodationStatus,
} from "@/lib/accommodations";
import type {
  Accommodation,
  AccommodationFilters,
  AccommodationSort,
} from "@/types/accommodation";
import {
  ACCOMMODATION_TYPE_LABELS,
  ACCOMMODATION_STATUS_CONFIG,
  DEFAULT_ACCOMMODATION_FILTERS,
  DEFAULT_ACCOMMODATION_SORT,
  DEFAULT_PAGINATION,
} from "@/types/accommodation";

export default function AccommodationsAdmin() {
  const navigate = useNavigate();

  // State management
  const [accommodations, setAccommodations] = useState<Accommodation[]>([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState<AccommodationFilters>(
    DEFAULT_ACCOMMODATION_FILTERS
  );
  const [sort, setSort] = useState<AccommodationSort>(
    DEFAULT_ACCOMMODATION_SORT
  );
  const [pagination, setPagination] = useState(DEFAULT_PAGINATION);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [accommodationToDelete, setAccommodationToDelete] = useState<
    string | null
  >(null);

  // Load accommodations
  const loadAccommodations = async () => {
    try {
      setLoading(true);
      const response = await getAccommodations(filters, sort, {
        page: pagination.page,
        limit: pagination.limit,
      });

      setAccommodations(response.data);
      setPagination(response.pagination);
    } catch (error) {
      console.error("Error loading accommodations:", error);
      toast.error("Failed to load accommodations");
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount and when filters/sort/pagination change
  useEffect(() => {
    loadAccommodations();
  }, [filters, sort, pagination.page]);

  // Handle search
  const handleSearch = (value: string) => {
    setFilters((prev) => ({ ...prev, search: value }));
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof AccommodationFilters, value: any) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  // Handle sort changes
  const handleSort = (field: AccommodationSort["field"]) => {
    setSort((prev) => ({
      field,
      direction:
        prev.field === field && prev.direction === "asc" ? "desc" : "asc",
    }));
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    setPagination((prev) => ({ ...prev, page }));
  };

  // Handle delete
  const handleDelete = async (id: string) => {
    try {
      const result = await deleteAccommodation(id);
      if (result.success) {
        toast.success("Accommodation deleted successfully");
        loadAccommodations();
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      toast.error("Failed to delete accommodation");
    }
    setDeleteDialogOpen(false);
    setAccommodationToDelete(null);
  };

  // Handle featured toggle
  const handleToggleFeatured = async (id: string, featured: boolean) => {
    try {
      const result = await toggleAccommodationFeatured(id, !featured);
      if (result.success) {
        toast.success(
          `Accommodation ${!featured ? "featured" : "unfeatured"} successfully`
        );
        loadAccommodations();
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      toast.error("Failed to update accommodation");
    }
  };

  // Handle status toggle
  const handleStatusToggle = async (id: string, currentStatus: string) => {
    const newStatus =
      currentStatus === "published" ? "unpublished" : "published";
    try {
      const result = await updateAccommodationStatus(id, newStatus as any);
      if (result.success) {
        toast.success(`Accommodation ${newStatus} successfully`);
        loadAccommodations();
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      toast.error("Failed to update accommodation status");
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Accommodations</h1>
          <p className="text-gray-600 mt-2">Manage rooms, suites, and tents</p>
        </div>
        <Button
          onClick={() => navigate("/admin/accommodations/new")}
          className="bg-amber-600 hover:bg-amber-700"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Accommodation
        </Button>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search accommodations..."
                  value={filters.search}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Type Filter */}
            <Select
              value={filters.type}
              onValueChange={(value) => handleFilterChange("type", value)}
            >
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                {Object.entries(ACCOMMODATION_TYPE_LABELS).map(
                  ([key, label]) => (
                    <SelectItem key={key} value={key}>
                      {label}
                    </SelectItem>
                  )
                )}
              </SelectContent>
            </Select>

            {/* Status Filter */}
            <Select
              value={filters.status}
              onValueChange={(value) => handleFilterChange("status", value)}
            >
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="published">Published</SelectItem>
                <SelectItem value="unpublished">Unpublished</SelectItem>
              </SelectContent>
            </Select>

            {/* Featured Filter */}
            <Select
              value={filters.featured.toString()}
              onValueChange={(value) =>
                handleFilterChange(
                  "featured",
                  value === "true" ? true : value === "false" ? false : "all"
                )
              }
            >
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder="Filter by featured" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="true">Featured</SelectItem>
                <SelectItem value="false">Not Featured</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Accommodations Table */}
      <Card>
        <CardContent className="p-0">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600"></div>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort("name")}
                  >
                    <div className="flex items-center">
                      Name
                      <ArrowUpDown className="ml-2 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort("type")}
                  >
                    <div className="flex items-center">
                      Type
                      <ArrowUpDown className="ml-2 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort("price_range")}
                  >
                    <div className="flex items-center">
                      Price Range
                      <ArrowUpDown className="ml-2 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort("capacity")}
                  >
                    <div className="flex items-center">
                      Capacity
                      <ArrowUpDown className="ml-2 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Featured</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {accommodations.length === 0 ? (
                  <TableRow>
                    <TableCell
                      colSpan={7}
                      className="text-center py-12 text-gray-500"
                    >
                      No accommodations found
                    </TableCell>
                  </TableRow>
                ) : (
                  accommodations.map((accommodation) => (
                    <TableRow
                      key={accommodation.id}
                      className="hover:bg-gray-50"
                    >
                      <TableCell className="font-medium">
                        {accommodation.name}
                      </TableCell>
                      <TableCell>
                        {ACCOMMODATION_TYPE_LABELS[accommodation.type]}
                      </TableCell>
                      <TableCell>{accommodation.price_range}</TableCell>
                      <TableCell>{accommodation.capacity} guests</TableCell>
                      <TableCell>
                        <Badge
                          className={`${
                            ACCOMMODATION_STATUS_CONFIG[accommodation.status]
                              .bgColor
                          } ${
                            ACCOMMODATION_STATUS_CONFIG[accommodation.status]
                              .color
                          }`}
                        >
                          {
                            ACCOMMODATION_STATUS_CONFIG[accommodation.status]
                              .label
                          }
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() =>
                            handleToggleFeatured(
                              accommodation.id,
                              accommodation.featured
                            )
                          }
                          className="p-1"
                        >
                          {accommodation.featured ? (
                            <Star className="h-4 w-4 text-amber-500 fill-current" />
                          ) : (
                            <StarOff className="h-4 w-4 text-gray-400" />
                          )}
                        </Button>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              handleStatusToggle(
                                accommodation.id,
                                accommodation.status
                              )
                            }
                            className="p-1"
                          >
                            {accommodation.status === "published" ? (
                              <EyeOff className="h-4 w-4" />
                            ) : (
                              <Eye className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              navigate(
                                `/admin/accommodations/edit/${accommodation.id}`
                              )
                            }
                            className="p-1"
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setAccommodationToDelete(accommodation.id);
                              setDeleteDialogOpen(true);
                            }}
                            className="p-1 text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-500">
            Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
            {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
            {pagination.total} results
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page - 1)}
              disabled={pagination.page === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <div className="flex items-center gap-1">
              {Array.from({ length: pagination.totalPages }, (_, i) => i + 1)
                .filter(
                  (page) =>
                    page === 1 ||
                    page === pagination.totalPages ||
                    Math.abs(page - pagination.page) <= 1
                )
                .map((page, index, array) => (
                  <React.Fragment key={page}>
                    {index > 0 && array[index - 1] !== page - 1 && (
                      <span className="px-2 text-gray-400">...</span>
                    )}
                    <Button
                      variant={page === pagination.page ? "default" : "outline"}
                      size="sm"
                      onClick={() => handlePageChange(page)}
                      className={
                        page === pagination.page
                          ? "bg-amber-600 hover:bg-amber-700"
                          : ""
                      }
                    >
                      {page}
                    </Button>
                  </React.Fragment>
                ))}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(pagination.page + 1)}
              disabled={pagination.page === pagination.totalPages}
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              accommodation and all associated images.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() =>
                accommodationToDelete && handleDelete(accommodationToDelete)
              }
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
