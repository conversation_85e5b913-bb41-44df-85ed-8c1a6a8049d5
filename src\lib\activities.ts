// =====================================================
// Activities Service Layer
// =====================================================
// Service functions for activity CRUD operations and image management

import { supabase } from "./supabase";
import type {
  Activity,
  ActivityCreateRequest,
  ActivityUpdateRequest,
  ActivityFilters,
  ActivitySort,
  ActivityListResponse,
  ActivityResponse,
  ActivityImageUploadResponse,
  BulkActivityOperation,
  BulkActivityOperationResponse,
  ActivityStats,
} from "@/types/activity";

// =====================================================
// CRUD Operations
// =====================================================

/**
 * Get activities with filtering, sorting, and pagination
 */
export async function getActivities(
  filters: ActivityFilters,
  sort: ActivitySort,
  pagination: { page: number; limit: number }
): Promise<ActivityListResponse> {
  try {
    let query = supabase.from("activities").select("*", { count: "exact" });

    // Apply filters
    if (filters.search) {
      query = query.or(
        `title.ilike.%${filters.search}%,description.ilike.%${filters.search}%`
      );
    }

    if (filters.category !== "all") {
      query = query.eq("category", filters.category);
    }

    if (filters.status !== "all") {
      query = query.eq("status", filters.status);
    }

    if (filters.featured !== "all") {
      query = query.eq("featured", filters.featured);
    }

    // Apply sorting
    query = query.order(sort.field, { ascending: sort.direction === "asc" });

    // Apply pagination
    const from = (pagination.page - 1) * pagination.limit;
    const to = from + pagination.limit - 1;
    query = query.range(from, to);

    const { data, error, count } = await query;

    if (error) throw error;

    const totalPages = Math.ceil((count || 0) / pagination.limit);

    return {
      data: data || [],
      pagination: {
        page: pagination.page,
        limit: pagination.limit,
        total: count || 0,
        totalPages,
      },
      filters,
      sort,
    };
  } catch (error) {
    console.error("Error fetching activities:", error);
    throw error;
  }
}

/**
 * Get a single activity by ID
 */
export async function getActivity(id: string): Promise<ActivityResponse> {
  try {
    const { data, error } = await supabase
      .from("activities")
      .select("*")
      .eq("id", id)
      .single();

    if (error) throw error;

    return {
      data,
      success: true,
    };
  } catch (error) {
    console.error("Error fetching activity:", error);
    return {
      data: null as any,
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to fetch activity",
    };
  }
}

/**
 * Create a new activity
 */
export async function createActivity(
  activityData: ActivityCreateRequest
): Promise<ActivityResponse> {
  try {
    const { data, error } = await supabase
      .from("activities")
      .insert([activityData])
      .select()
      .single();

    if (error) throw error;

    return {
      data,
      success: true,
      message: "Activity created successfully",
    };
  } catch (error) {
    console.error("Error creating activity:", error);
    return {
      data: null as any,
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to create activity",
    };
  }
}

/**
 * Update an existing activity
 */
export async function updateActivity(
  activityData: ActivityUpdateRequest
): Promise<ActivityResponse> {
  try {
    const { id, ...updateData } = activityData;

    const { data, error } = await supabase
      .from("activities")
      .update(updateData)
      .eq("id", id)
      .select()
      .single();

    if (error) throw error;

    return {
      data,
      success: true,
      message: "Activity updated successfully",
    };
  } catch (error) {
    console.error("Error updating activity:", error);
    return {
      data: null as any,
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to update activity",
    };
  }
}

/**
 * Delete an activity
 */
export async function deleteActivity(
  id: string
): Promise<{ success: boolean; message: string }> {
  try {
    // First, get the activity to retrieve image URLs for cleanup
    const { data: activity } = await supabase
      .from("activities")
      .select("images")
      .eq("id", id)
      .single();

    // Delete the activity record
    const { error } = await supabase.from("activities").delete().eq("id", id);

    if (error) throw error;

    // Clean up associated images
    if (activity?.images && Array.isArray(activity.images)) {
      await Promise.all(
        activity.images.map((imageUrl: string) => deleteActivityImage(imageUrl))
      );
    }

    return {
      success: true,
      message: "Activity deleted successfully",
    };
  } catch (error) {
    console.error("Error deleting activity:", error);
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to delete activity",
    };
  }
}

// =====================================================
// Status Management
// =====================================================

/**
 * Update activity status
 */
export async function updateActivityStatus(
  id: string,
  status: "draft" | "published" | "unpublished"
): Promise<ActivityResponse> {
  return updateActivity({ id, status });
}

/**
 * Toggle activity featured status
 */
export async function toggleActivityFeatured(
  id: string,
  featured: boolean
): Promise<ActivityResponse> {
  return updateActivity({ id, featured });
}

// =====================================================
// Image Management
// =====================================================

/**
 * Upload activity image to Supabase Storage
 */
export async function uploadActivityImage(
  file: File,
  activityId: string
): Promise<ActivityImageUploadResponse> {
  try {
    // Generate unique filename
    const fileExt = file.name.split(".").pop();
    const fileName = `${activityId}/${Date.now()}.${fileExt}`;

    const { error } = await supabase.storage
      .from("activity-images")
      .upload(fileName, file, {
        cacheControl: "3600",
        upsert: false,
      });

    if (error) throw error;

    // Get public URL
    const {
      data: { publicUrl },
    } = supabase.storage.from("activity-images").getPublicUrl(fileName);

    return {
      url: publicUrl,
      path: fileName,
      success: true,
    };
  } catch (error) {
    console.error("Error uploading image:", error);
    return {
      url: "",
      path: "",
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to upload image",
    };
  }
}

/**
 * Delete activity image from Supabase Storage
 */
export async function deleteActivityImage(
  imageUrl: string
): Promise<{ success: boolean; message?: string }> {
  try {
    // Extract file path from URL
    const url = new URL(imageUrl);
    const pathParts = url.pathname.split("/");
    const fileName = pathParts[pathParts.length - 1];
    const folderName = pathParts[pathParts.length - 2];
    const filePath = `${folderName}/${fileName}`;

    const { error } = await supabase.storage
      .from("activity-images")
      .remove([filePath]);

    if (error) throw error;

    return { success: true };
  } catch (error) {
    console.error("Error deleting image:", error);
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to delete image",
    };
  }
}

// =====================================================
// Bulk Operations
// =====================================================

/**
 * Perform bulk operations on activities
 */
export async function bulkUpdateActivities(
  operation: BulkActivityOperation
): Promise<BulkActivityOperationResponse> {
  try {
    let updateData: Partial<Activity> = {};

    switch (operation.action) {
      case "publish":
        updateData = { status: "published" };
        break;
      case "unpublish":
        updateData = { status: "unpublished" };
        break;
      case "feature":
        updateData = { featured: true };
        break;
      case "unfeature":
        updateData = { featured: false };
        break;
      case "delete":
        // Handle delete separately
        const deletePromises = operation.activity_ids.map((id) =>
          deleteActivity(id)
        );
        const deleteResults = await Promise.all(deletePromises);
        const successCount = deleteResults.filter((r) => r.success).length;
        const failedCount = deleteResults.length - successCount;

        return {
          success: failedCount === 0,
          updated_count: successCount,
          failed_count: failedCount,
          message: `${successCount} activities deleted successfully${
            failedCount > 0 ? `, ${failedCount} failed` : ""
          }`,
        };
    }

    const { data, error } = await supabase
      .from("activities")
      .update(updateData)
      .in("id", operation.activity_ids)
      .select("id");

    if (error) throw error;

    return {
      success: true,
      updated_count: data?.length || 0,
      failed_count: 0,
      message: `${data?.length || 0} activities updated successfully`,
    };
  } catch (error) {
    console.error("Error performing bulk operation:", error);
    return {
      success: false,
      updated_count: 0,
      failed_count: operation.activity_ids.length,
      message: error instanceof Error ? error.message : "Bulk operation failed",
    };
  }
}

// =====================================================
// Statistics and Analytics
// =====================================================

/**
 * Get activity statistics
 */
export async function getActivityStats(): Promise<ActivityStats> {
  try {
    const { data, error } = await supabase.rpc("get_activity_stats");

    if (error) throw error;

    return (
      data || {
        total_activities: 0,
        published_activities: 0,
        draft_activities: 0,
        featured_activities: 0,
        activities_by_category: {},
      }
    );
  } catch (error) {
    console.error("Error fetching activity stats:", error);
    return {
      total_activities: 0,
      published_activities: 0,
      draft_activities: 0,
      featured_activities: 0,
      activities_by_category: {},
    };
  }
}

// =====================================================
// Public API (for frontend website)
// =====================================================

/**
 * Get published activities for public display
 */
export async function getPublishedActivities(): Promise<Activity[]> {
  try {
    const { data, error } = await supabase
      .from("activities_public")
      .select("*")
      .order("featured", { ascending: false })
      .order("created_at", { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error("Error fetching published activities:", error);
    return [];
  }
}

/**
 * Get featured activities for homepage
 */
export async function getFeaturedActivities(): Promise<Activity[]> {
  try {
    const { data, error } = await supabase
      .from("activities_public")
      .select("*")
      .eq("featured", true)
      .order("created_at", { ascending: false })
      .limit(6);

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error("Error fetching featured activities:", error);
    return [];
  }
}

/**
 * Get activities by category for public display
 */
export async function getActivitiesByCategory(
  category: string
): Promise<Activity[]> {
  try {
    const { data, error } = await supabase
      .from("activities_public")
      .select("*")
      .eq("category", category)
      .order("featured", { ascending: false })
      .order("created_at", { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error("Error fetching activities by category:", error);
    return [];
  }
}

/**
 * Search published activities
 */
export async function searchPublishedActivities(
  searchTerm: string
): Promise<Activity[]> {
  try {
    const { data, error } = await supabase
      .from("activities_public")
      .select("*")
      .or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`)
      .order("featured", { ascending: false })
      .order("created_at", { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error("Error searching published activities:", error);
    return [];
  }
}
