import { useNavigate } from "react-router-dom";
import { usePageTitle } from "@/hooks/use-page-title";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { Badge } from "@/components/ui/badge";
import { accommodations } from "@/data/accommodations";
import { Users, Bed, Wifi, Coffee, Shield, Car } from "lucide-react";

const Accommodations = () => {
  const navigate = useNavigate();
  usePageTitle("Accommodations");

  const handleBookNow = (accommodationId: string) => {
    navigate(`/booking?accommodation=${accommodationId}`);
  };

  const getIconForAmenity = (amenity: string) => {
    if (amenity.toLowerCase().includes("wifi"))
      return <Wifi className="h-4 w-4" />;
    if (
      amenity.toLowerCase().includes("coffee") ||
      amenity.toLowerCase().includes("tea")
    )
      return <Coffee className="h-4 w-4" />;
    if (amenity.toLowerCase().includes("safe"))
      return <Shield className="h-4 w-4" />;
    if (amenity.toLowerCase().includes("parking"))
      return <Car className="h-4 w-4" />;
    return null;
  };

  return (
    <div className="flex flex-col w-full min-h-screen">
      {/* Hero Section */}
      <section className="relative w-full h-[40vh] bg-gradient-to-r from-[#0d1a0e] to-[#3a1c07]">
        <div className="absolute inset-0">
          <img
            src="/images/accommodations-hero.jpg"
            alt="Malombo Accommodations"
            className="w-full h-full object-cover opacity-30"
          />
        </div>
        <div className="relative z-10 container mx-auto h-full flex flex-col justify-center px-4">
          {/* Breadcrumb */}
          {/* <div className="mb-14">
            <Breadcrumb>
              <BreadcrumbList className="text-white/80">
                <BreadcrumbItem>
                  <BreadcrumbLink
                    onClick={() => navigate("/")}
                    className="text-white/80 hover:text-white cursor-pointer"
                  >
                    Home
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="text-white/60" />
                <BreadcrumbItem>
                  <BreadcrumbPage className="text-white font-semibold">
                    Accommodations
                  </BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div> */}

          <h1 className="text-[2rem] md:text-6xl text-center mb-4 playfair-display-heading text-white">
            Our <span className="text-[#DAA520]">Accommodations</span>
          </h1>
          <p className="text-[1.3rem] md:text-2xl text-center mb-8 max-w-3xl mx-auto playfair-display-subheading text-white/90">
            Choose from our carefully designed accommodations, each offering a
            unique way to experience the African wilderness
          </p>
        </div>
      </section>

      {/* Accommodations Grid */}
      <section className="py-12 w-[95%] mx-auto">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
            {accommodations.map((accommodation) => (
              <Card
                key={accommodation.id}
                className="overflow-hidden group hover:shadow-xl transition-all duration-300 bg-white border-t-4 border-t-[#8B4513] h-full flex flex-col"
              >
                {/* Image Section */}
                <div className="relative">
                  <AspectRatio ratio={16 / 10}>
                    <img
                      src={accommodation.image}
                      alt={accommodation.title}
                      className="object-cover w-full h-full transition-transform duration-300 group-hover:scale-105"
                    />
                  </AspectRatio>
                  <Badge
                    className={`absolute top-3 left-3 ${
                      accommodation.category === "luxury"
                        ? "bg-[#DAA520] text-white"
                        : accommodation.category === "premium"
                        ? "bg-[#8B4513] text-white"
                        : "bg-[#2C5530] text-white"
                    } capitalize font-semibold`}
                  >
                    {accommodation.category}
                  </Badge>
                  <div className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1">
                    <div className="text-lg font-bold text-[#8B4513]">
                      ${accommodation.pricePerNight}
                    </div>
                    <div className="text-xs text-neutral-600 text-center">
                      per night
                    </div>
                  </div>
                </div>

                {/* Content Section */}
                <div className="p-5 flex flex-col flex-grow">
                  <CardHeader className="p-0 mb-4">
                    <CardTitle className="playfair-display-heading text-xl font-bold text-[#8B4513] mb-2 line-clamp-1">
                      {accommodation.title}
                    </CardTitle>
                    <CardDescription className="text-sm text-neutral-600 leading-relaxed line-clamp-3">
                      {accommodation.description}
                    </CardDescription>
                  </CardHeader>

                  {/* Key Details */}
                  <div className="grid grid-cols-2 gap-3 mb-4 p-3 bg-neutral-50 rounded-lg text-sm">
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-[#8B4513]" />
                      <span>Up to {accommodation.maxGuests}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Bed className="h-4 w-4 text-[#8B4513]" />
                      <span className="truncate">
                        {accommodation.bedConfiguration.split(" ")[0]}{" "}
                        {accommodation.bedConfiguration.split(" ")[1]}
                      </span>
                    </div>
                    <div className="col-span-2 text-xs text-neutral-500">
                      <strong>Size:</strong> {accommodation.size}
                    </div>
                  </div>

                  {/* Top Amenities */}
                  <div className="mb-4 flex-grow">
                    <h4 className="font-semibold text-[#8B4513] mb-2 text-sm">
                      Top Amenities
                    </h4>
                    <div className="grid grid-cols-1 gap-1">
                      {accommodation.amenities
                        .slice(0, 4)
                        .map((amenity, idx) => (
                          <div
                            key={idx}
                            className="flex items-center gap-2 text-xs text-neutral-600"
                          >
                            {getIconForAmenity(amenity) || (
                              <div className="w-1.5 h-1.5 bg-[#DAA520] rounded-full flex-shrink-0" />
                            )}
                            <span className="truncate">{amenity}</span>
                          </div>
                        ))}
                      {accommodation.amenities.length > 4 && (
                        <div className="text-xs text-[#8B4513] font-medium mt-1">
                          +{accommodation.amenities.length - 4} more amenities
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Book Button */}
                  <CardFooter className="p-0 mt-auto">
                    <Button
                      size="sm"
                      className="w-full bg-gradient-to-r from-[#8B4513] to-[#DAA520] hover:from-[#DAA520] hover:to-[#8B4513] text-white transition-all duration-300 shadow-md hover:shadow-lg"
                      onClick={() => handleBookNow(accommodation.id)}
                    >
                      Book Now
                    </Button>
                  </CardFooter>
                </div>
              </Card>
            ))}
          </div>

          {/* Additional Information Section */}
          <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="p-6 bg-gradient-to-br from-[#8B4513]/5 to-[#DAA520]/5 border-[#8B4513]/20">
              <div className="text-center">
                <div className="text-2xl mb-2">🏕️</div>
                <h3 className="font-semibold text-[#8B4513] mb-2">
                  All Inclusive
                </h3>
                <p className="text-sm text-neutral-600">
                  All meals, activities, and transfers included in your stay
                </p>
              </div>
            </Card>
            <Card className="p-6 bg-gradient-to-br from-[#8B4513]/5 to-[#DAA520]/5 border-[#8B4513]/20">
              <div className="text-center">
                <div className="text-2xl mb-2">🌿</div>
                <h3 className="font-semibold text-[#8B4513] mb-2">
                  Eco-Friendly
                </h3>
                <p className="text-sm text-neutral-600">
                  Sustainable practices and solar-powered facilities
                </p>
              </div>
            </Card>
            <Card className="p-6 bg-gradient-to-br from-[#8B4513]/5 to-[#DAA520]/5 border-[#8B4513]/20">
              <div className="text-center">
                <div className="text-2xl mb-2">🦁</div>
                <h3 className="font-semibold text-[#8B4513] mb-2">
                  Wildlife Rich
                </h3>
                <p className="text-sm text-neutral-600">
                  Big Five and over 2000 bird species in the area
                </p>
              </div>
            </Card>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-[#8B4513]/10 to-[#DAA520]/10 w-[90%] mx-auto">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl playfair-display-heading text-[#8B4513] mb-4">
            Need Help Choosing?
          </h2>
          <p className="text-lg text-neutral-600 mb-8 max-w-2xl mx-auto">
            Our safari experts are here to help you select the perfect
            accommodation for your African adventure.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              variant="outline"
              size="lg"
              className="border-[#8B4513] text-[#8B4513] hover:bg-[#8B4513] hover:text-white"
            >
              Contact Our Experts
            </Button>
            <Button
              size="lg"
              className="bg-gradient-to-r from-[#8B4513] to-[#DAA520] hover:from-[#DAA520] hover:to-[#8B4513] text-white"
              onClick={() => navigate("/booking")}
            >
              Start Booking Process
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Accommodations;
