# Netlify redirects for SPA routing
# This file handles client-side routing for React Router

# Redirect all routes to index.html for SPA routing
/*    /index.html   200

# Optional: Redirect www to non-www (uncomment if needed)
# https://www.malombocamp.com/*  https://malombocamp.com/:splat  301!

# Optional: Force HTTPS (uncomment if needed)
# http://malombocamp.com/*  https://malombocamp.com/:splat  301!

# API routes (if you have any backend APIs, add them here)
# /api/*  https://your-api-domain.com/api/:splat  200

# Static assets caching headers
/images/*  /images/:splat  200
  Cache-Control: public, max-age=********, immutable

/assets/*  /assets/:splat  200
  Cache-Control: public, max-age=********, immutable

# Favicon and manifest files
/favicon.ico  /favicon.ico  200
  Cache-Control: public, max-age=86400

/manifest.json  /manifest.json  200
  Cache-Control: public, max-age=86400
