-- =====================================================
-- Malombo Admin Panel - Bookings Table Setup
-- =====================================================
-- This script creates the bookings table and related infrastructure
-- for managing guest reservations in the admin panel.

-- Drop existing table if it exists (for development)
DROP TABLE IF EXISTS public.bookings CASCADE;

-- Create bookings table
CREATE TABLE public.bookings (
    -- Primary key
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- Guest information
    guest_full_name TEXT NOT NULL,
    email TEXT NOT NULL,
    phone TEXT NOT NULL,
    country TEXT NOT NULL,
    
    -- Party composition (align with child pricing policy)
    adults INTEGER NOT NULL CHECK (adults > 0),
    children_5_13 INTEGER NOT NULL DEFAULT 0 CHECK (children_5_13 >= 0), -- Half price
    children_0_4 INTEGER NOT NULL DEFAULT 0 CHECK (children_0_4 >= 0),   -- Free
    
    -- Stay details
    accommodation_id UUID NOT NULL REFERENCES public.accommodations(id),
    check_in DATE NOT NULL,
    check_out DATE NOT NULL,
    nights INTEGER GENERATED ALWAYS AS (check_out - check_in) STORED,
    
    -- Activities (array of activity IDs)
    activity_ids UUID[] DEFAULT '{}',
    
    -- Pricing breakdown
    currency TEXT NOT NULL DEFAULT 'USD' CHECK (currency IN ('USD', 'EUR', 'GBP', 'TZS')),
    base_amount NUMERIC(10,2) NOT NULL CHECK (base_amount >= 0),
    discounts NUMERIC(10,2) NOT NULL DEFAULT 0 CHECK (discounts >= 0),
    taxes NUMERIC(10,2) NOT NULL DEFAULT 0 CHECK (taxes >= 0),
    total_amount NUMERIC(10,2) NOT NULL CHECK (total_amount >= 0),
    
    -- Status management
    status TEXT NOT NULL CHECK (status IN ('new', 'confirmed', 'cancelled', 'completed')) DEFAULT 'new',
    
    -- Payment tracking
    payment_status TEXT NOT NULL CHECK (payment_status IN ('unpaid', 'deposit_paid', 'paid', 'refunded')) DEFAULT 'unpaid',
    payment_method TEXT,
    payment_reference TEXT,
    
    -- Booking metadata
    source TEXT NOT NULL DEFAULT 'website' CHECK (source IN ('website', 'email', 'whatsapp', 'agent', 'phone')),
    notes_admin TEXT,
    notes_guest TEXT,
    
    -- Audit trail
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status_history JSONB DEFAULT '[]'::jsonb,
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id),
    
    -- Constraints
    CONSTRAINT valid_dates CHECK (check_out > check_in),
    CONSTRAINT valid_party_size CHECK (adults + children_5_13 + children_0_4 > 0)
);

-- Create indexes for performance
CREATE INDEX idx_bookings_status ON public.bookings(status);
CREATE INDEX idx_bookings_check_in ON public.bookings(check_in);
CREATE INDEX idx_bookings_payment_status ON public.bookings(payment_status);
CREATE INDEX idx_bookings_accommodation ON public.bookings(accommodation_id);
CREATE INDEX idx_bookings_guest_email ON public.bookings(email);
CREATE INDEX idx_bookings_created_at ON public.bookings(created_at);
CREATE INDEX idx_bookings_status_checkin ON public.bookings(status, check_in);

-- GIN index for activity_ids array
CREATE INDEX idx_bookings_activity_ids ON public.bookings USING GIN(activity_ids);

-- GIN index for status_history JSON
CREATE INDEX idx_bookings_status_history ON public.bookings USING GIN(status_history);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_bookings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_bookings_updated_at
    BEFORE UPDATE ON public.bookings
    FOR EACH ROW
    EXECUTE FUNCTION update_bookings_updated_at();

-- Create trigger to track status changes
CREATE OR REPLACE FUNCTION track_booking_status_changes()
RETURNS TRIGGER AS $$
BEGIN
    -- Only track if status actually changed
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        NEW.status_history = COALESCE(OLD.status_history, '[]'::jsonb) || 
            jsonb_build_object(
                'from_status', OLD.status,
                'to_status', NEW.status,
                'changed_at', NOW(),
                'changed_by', NEW.updated_by
            );
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_track_booking_status_changes
    BEFORE UPDATE ON public.bookings
    FOR EACH ROW
    EXECUTE FUNCTION track_booking_status_changes();

-- Enable Row Level Security
ALTER TABLE public.bookings ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Admin users can do everything
CREATE POLICY "Admin users can manage all bookings" ON public.bookings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Staff users can view and update bookings (but not delete)
CREATE POLICY "Staff users can view and update bookings" ON public.bookings
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('admin', 'staff')
        )
    );

CREATE POLICY "Staff users can update bookings" ON public.bookings
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('admin', 'staff')
        )
    );

CREATE POLICY "Staff users can insert bookings" ON public.bookings
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('admin', 'staff')
        )
    );

-- Create view for public booking statistics (no sensitive data)
CREATE OR REPLACE VIEW public.booking_stats AS
SELECT 
    DATE_TRUNC('month', check_in) as month,
    status,
    COUNT(*) as booking_count,
    SUM(total_amount) as total_revenue,
    AVG(total_amount) as avg_booking_value,
    SUM(adults + children_5_13 + children_0_4) as total_guests
FROM public.bookings
WHERE status != 'cancelled'
GROUP BY DATE_TRUNC('month', check_in), status;

-- Grant access to the view
GRANT SELECT ON public.booking_stats TO authenticated;

-- Add some sample data for testing
INSERT INTO public.bookings (
    guest_full_name, email, phone, country,
    adults, children_5_13, children_0_4,
    accommodation_id, check_in, check_out,
    activity_ids, currency, base_amount, discounts, taxes, total_amount,
    status, payment_status, payment_method, source, notes_guest
) VALUES 
(
    'John Smith',
    '<EMAIL>',
    '******-0123',
    'United States',
    2, 0, 0,
    (SELECT id FROM public.accommodations WHERE name = 'Luxury Safari Suite' LIMIT 1),
    '2024-10-15',
    '2024-10-18',
    ARRAY[(SELECT id FROM public.activities WHERE title LIKE '%Game Drive%' LIMIT 1)]::UUID[],
    'USD',
    1350.00,
    0.00,
    135.00,
    1485.00,
    'confirmed',
    'deposit_paid',
    'credit_card',
    'website',
    'Celebrating our anniversary - please arrange something special'
),
(
    'Sarah Johnson',
    '<EMAIL>',
    '+44-20-7946-0958',
    'United Kingdom',
    2, 1, 0,
    (SELECT id FROM public.accommodations WHERE type = 'room' LIMIT 1),
    '2024-11-05',
    '2024-11-08',
    '{}',
    'USD',
    900.00,
    45.00,
    85.50,
    940.50,
    'new',
    'unpaid',
    NULL,
    'email',
    'Looking forward to our first safari experience'
);

-- Create helper function to get booking with accommodation and activity details
CREATE OR REPLACE FUNCTION get_booking_details(booking_id UUID)
RETURNS TABLE (
    booking_data JSONB,
    accommodation_data JSONB,
    activities_data JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        to_jsonb(b.*) as booking_data,
        to_jsonb(a.*) as accommodation_data,
        COALESCE(
            (SELECT jsonb_agg(to_jsonb(act.*))
             FROM public.activities act
             WHERE act.id = ANY(b.activity_ids)),
            '[]'::jsonb
        ) as activities_data
    FROM public.bookings b
    LEFT JOIN public.accommodations a ON a.id = b.accommodation_id
    WHERE b.id = booking_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_booking_details(UUID) TO authenticated;

COMMENT ON TABLE public.bookings IS 'Guest booking reservations with full details';
COMMENT ON COLUMN public.bookings.nights IS 'Automatically calculated from check_in and check_out dates';
COMMENT ON COLUMN public.bookings.activity_ids IS 'Array of activity UUIDs for this booking';
COMMENT ON COLUMN public.bookings.status_history IS 'JSON array tracking all status changes with timestamps';
