-- =====================================================
-- Malombo Admin Panel - Seed Admin Users
-- =====================================================
-- This file creates initial admin and staff users for testing.
-- 
-- ⚠️  IMPORTANT: These are TEST ACCOUNTS with default passwords!
-- ⚠️  Change passwords and remove test accounts before production!
-- 
-- Safe to run multiple times (idempotent)
-- =====================================================

-- =====================================================
-- STEP 1: Create Users in Supabase Auth Dashboard First!
-- =====================================================
-- Before running this script, create these users in Supabase Auth:
--
-- 1. Go to Supabase Dashboard → Authentication → Users
-- 2. Click "Add User" and create:
--    - Email: <EMAIL>, Password: Admin123!
--    - Email: <EMAIL>, Password: Staff123!
--    - Email: <EMAIL>, Password: Manager123!
-- 3. Then run this script to create their profiles

-- =====================================================
-- STEP 2: Automatic Profile Creation (Smart Approach)
-- =====================================================
-- This script will automatically find existing auth users and create profiles

-- Clear existing test data (for idempotency)
DELETE FROM public.profiles
WHERE email IN (
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
);

-- =====================================================
-- SMART INSERT: Create profiles for existing auth users
-- =====================================================

-- Create admin profile (if auth user exists)
INSERT INTO public.profiles (
    id,
    email,
    role,
    first_name,
    last_name,
    phone,
    is_active,
    created_at,
    updated_at
)
SELECT
    u.id,
    u.email,
    'admin' as role,
    'Admin' as first_name,
    'User' as last_name,
    '+255 123 456 789' as phone,
    true as is_active,
    NOW() as created_at,
    NOW() as updated_at
FROM auth.users u
WHERE u.email = '<EMAIL>'
ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    role = EXCLUDED.role,
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    phone = EXCLUDED.phone,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Create staff profile (if auth user exists)
INSERT INTO public.profiles (
    id,
    email,
    role,
    first_name,
    last_name,
    phone,
    is_active,
    created_at,
    updated_at
)
SELECT
    u.id,
    u.email,
    'staff' as role,
    'Staff' as first_name,
    'Member' as last_name,
    '+255 123 456 790' as phone,
    true as is_active,
    NOW() as created_at,
    NOW() as updated_at
FROM auth.users u
WHERE u.email = '<EMAIL>'
ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    role = EXCLUDED.role,
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    phone = EXCLUDED.phone,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- Create manager profile (admin role, if auth user exists)
INSERT INTO public.profiles (
    id,
    email,
    role,
    first_name,
    last_name,
    phone,
    is_active,
    created_at,
    updated_at
)
SELECT
    u.id,
    u.email,
    'admin' as role,
    'Camp' as first_name,
    'Manager' as last_name,
    '+255 123 456 791' as phone,
    true as is_active,
    NOW() as created_at,
    NOW() as updated_at
FROM auth.users u
WHERE u.email = '<EMAIL>'
ON CONFLICT (id) DO UPDATE SET
    email = EXCLUDED.email,
    role = EXCLUDED.role,
    first_name = EXCLUDED.first_name,
    last_name = EXCLUDED.last_name,
    phone = EXCLUDED.phone,
    is_active = EXCLUDED.is_active,
    updated_at = NOW();

-- =====================================================
-- HELPER QUERY: Get User UUIDs from Auth
-- =====================================================
-- Run this query to get the actual UUIDs after creating users in Supabase Auth:

/*
SELECT 
    id,
    email,
    created_at,
    email_confirmed_at
FROM auth.users 
WHERE email IN (
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
)
ORDER BY email;
*/

-- =====================================================
-- VERIFICATION QUERY
-- =====================================================
-- Run this query to verify the profiles were created correctly:

/*
SELECT 
    p.id,
    p.email,
    p.role,
    p.first_name,
    p.last_name,
    p.is_active,
    p.created_at,
    u.email_confirmed_at
FROM public.profiles p
LEFT JOIN auth.users u ON p.id = u.id
WHERE p.email LIKE '%@malombo.com'
ORDER BY p.role DESC, p.email;
*/

-- =====================================================
-- RESULTS AND VERIFICATION
-- =====================================================

-- Show results of profile creation
DO $$
DECLARE
    admin_count INTEGER;
    staff_count INTEGER;
    total_count INTEGER;
BEGIN
    -- Count created profiles
    SELECT COUNT(*) INTO admin_count FROM public.profiles WHERE role = 'admin';
    SELECT COUNT(*) INTO staff_count FROM public.profiles WHERE role = 'staff';
    SELECT COUNT(*) INTO total_count FROM public.profiles;

    RAISE NOTICE '=== PROFILE CREATION RESULTS ===';
    RAISE NOTICE 'Admin profiles created: %', admin_count;
    RAISE NOTICE 'Staff profiles created: %', staff_count;
    RAISE NOTICE 'Total profiles: %', total_count;
    RAISE NOTICE '';

    -- Check which test users were found
    IF EXISTS (SELECT 1 FROM public.profiles WHERE email = '<EMAIL>') THEN
        RAISE NOTICE '✅ <EMAIL> profile created successfully';
    ELSE
        RAISE NOTICE '❌ <EMAIL> not found - create user in Auth first';
    END IF;

    IF EXISTS (SELECT 1 FROM public.profiles WHERE email = '<EMAIL>') THEN
        RAISE NOTICE '✅ <EMAIL> profile created successfully';
    ELSE
        RAISE NOTICE '❌ <EMAIL> not found - create user in Auth first';
    END IF;

    IF EXISTS (SELECT 1 FROM public.profiles WHERE email = '<EMAIL>') THEN
        RAISE NOTICE '✅ <EMAIL> profile created successfully';
    ELSE
        RAISE NOTICE '❌ <EMAIL> not found - create user in Auth first';
    END IF;

    RAISE NOTICE '';
    RAISE NOTICE 'NEXT STEPS:';
    RAISE NOTICE '1. If any users are missing, create them in Supabase Auth Dashboard';
    RAISE NOTICE '2. Re-run this script to create their profiles';
    RAISE NOTICE '3. Test login at /admin/login';
    RAISE NOTICE '';
    RAISE NOTICE '⚠️  WARNING: Change passwords before production!';
END $$;
