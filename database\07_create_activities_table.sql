-- =====================================================
-- Malombo Admin Panel - Activities Table Setup
-- =====================================================
-- This script creates the activities table and related infrastructure
-- for managing activity listings in the admin panel.

-- Drop existing table if it exists (for development)
DROP TABLE IF EXISTS public.activities CASCADE;

-- Create activities table
CREATE TABLE public.activities (
    -- Primary key
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    
    -- Basic information
    title TEXT NOT NULL,
    category TEXT NOT NULL CHECK (category IN (
        'game_drive', 'boat_safari', 'walk_safari', 
        'cultural_tour', 'fishing', 'village_tour', 'youth_program'
    )),
    
    -- Descriptions
    description TEXT NOT NULL,
    
    -- Activity details
    duration TEXT NOT NULL, -- e.g., "3 hours", "Full Day"
    schedule TEXT, -- e.g., "Morning (6AM–10AM), Afternoon (2PM–6PM)"
    pricing TEXT NOT NULL, -- e.g., "$50 per person"
    
    -- Inclusions as JSON array
    inclusions JSONB DEFAULT '[]'::jsonb,
    
    -- Images stored as array of Supabase storage URLs
    images JSONB DEFAULT '[]'::jsonb,
    
    -- Status and visibility
    status TEXT NOT NULL CHECK (status IN ('draft', 'published', 'unpublished')) DEFAULT 'draft',
    featured BOOLEAN NOT NULL DEFAULT false,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id)
);

-- Create indexes for better performance
CREATE INDEX idx_activities_status ON public.activities(status);
CREATE INDEX idx_activities_category ON public.activities(category);
CREATE INDEX idx_activities_featured ON public.activities(featured);
CREATE INDEX idx_activities_created_at ON public.activities(created_at);

-- Enable Row Level Security
ALTER TABLE public.activities ENABLE ROW LEVEL SECURITY;

-- Create policies for activities table
-- Admin users can do everything
CREATE POLICY "Admin users can manage activities" ON public.activities
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Staff users can view and edit (but not delete)
CREATE POLICY "Staff users can view and edit activities" ON public.activities
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('admin', 'staff')
        )
    );

CREATE POLICY "Staff users can update activities" ON public.activities
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('admin', 'staff')
        )
    );

CREATE POLICY "Staff users can insert activities" ON public.activities
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('admin', 'staff')
        )
    );

-- Public users can only view published activities
CREATE POLICY "Public users can view published activities" ON public.activities
    FOR SELECT USING (status = 'published');

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_activities_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    NEW.updated_by = auth.uid();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_activities_updated_at
    BEFORE UPDATE ON public.activities
    FOR EACH ROW
    EXECUTE FUNCTION update_activities_updated_at();

-- Create trigger to set created_by on insert
CREATE OR REPLACE FUNCTION set_activities_created_by()
RETURNS TRIGGER AS $$
BEGIN
    NEW.created_by = auth.uid();
    NEW.updated_by = auth.uid();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_set_activities_created_by
    BEFORE INSERT ON public.activities
    FOR EACH ROW
    EXECUTE FUNCTION set_activities_created_by();

-- Grant permissions
GRANT ALL ON public.activities TO authenticated;
GRANT SELECT ON public.activities TO anon;

-- Create storage bucket for activity images
INSERT INTO storage.buckets (id, name, public)
VALUES ('activity-images', 'activity-images', true)
ON CONFLICT (id) DO NOTHING;

-- Create storage policies for activity images
-- Admin users can upload, update, and delete images
CREATE POLICY "Admin users can manage activity images" ON storage.objects
    FOR ALL USING (
        bucket_id = 'activity-images' AND
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role = 'admin'
        )
    );

-- Staff users can upload and update images
CREATE POLICY "Staff users can manage activity images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'activity-images' AND
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('admin', 'staff')
        )
    );

CREATE POLICY "Staff users can update activity images" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'activity-images' AND
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('admin', 'staff')
        )
    );

-- Public users can view images
CREATE POLICY "Public users can view activity images" ON storage.objects
    FOR SELECT USING (bucket_id = 'activity-images');

-- Add some sample data for testing
INSERT INTO public.activities (
    title, category, description, duration, schedule, pricing, inclusions, status, featured
) VALUES 
(
    'Game Drive – Morning Safari',
    'game_drive',
    'Experience the magic of the African wilderness on our morning game drive. Witness the incredible diversity of wildlife in their natural habitat as the sun rises over the Selous Game Reserve.',
    '3 hours',
    'Morning (6AM–9AM)',
    '$120 per person',
    '["Professional guide", "4WD safari vehicle", "Binoculars", "Bottled water", "Park fees"]'::jsonb,
    'published',
    true
),
(
    'Sunset Boat Safari',
    'boat_safari',
    'Glide along the mighty Rufiji River as the sun sets, offering spectacular views and excellent wildlife viewing opportunities from the water.',
    '2.5 hours',
    'Evening (4PM–6:30PM)',
    '$95 per person',
    '["Boat captain", "Life jackets", "Sunset drinks", "Snacks", "Photography assistance"]'::jsonb,
    'published',
    true
),
(
    'Walking Safari Adventure',
    'walk_safari',
    'Get up close with nature on foot with our experienced guides. Learn about animal tracks, medicinal plants, and the intricate ecosystem of the Selous.',
    '2 hours',
    'Morning (7AM–9AM) or Afternoon (4PM–6PM)',
    '$75 per person',
    '["Armed ranger", "Professional guide", "First aid kit", "Refreshments"]'::jsonb,
    'published',
    false
),
(
    'Cultural Village Tour',
    'cultural_tour',
    'Visit a local village and experience authentic Tanzanian culture, traditional crafts, and local customs.',
    '4 hours',
    'Morning (9AM–1PM)',
    '$85 per person',
    '["Local guide", "Village entry fees", "Traditional lunch", "Cultural demonstrations"]'::jsonb,
    'published',
    false
),
(
    'Fishing Expedition',
    'fishing',
    'Try your hand at fishing in the Rufiji River with traditional and modern techniques.',
    'Half day',
    'Morning (6AM–12PM)',
    '$110 per person',
    '["Fishing equipment", "Boat", "Guide", "Bait", "Lunch", "Drinks"]'::jsonb,
    'draft',
    false
);

-- Create view for public activity data (only published)
CREATE OR REPLACE VIEW public.activities_public AS
SELECT 
    id,
    title,
    category,
    description,
    duration,
    schedule,
    pricing,
    inclusions,
    images,
    featured,
    created_at
FROM public.activities
WHERE status = 'published'
ORDER BY featured DESC, created_at DESC;

-- Grant access to the public view
GRANT SELECT ON public.activities_public TO anon;
GRANT SELECT ON public.activities_public TO authenticated;

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Activities table and storage bucket created successfully!';
    RAISE NOTICE 'Sample data inserted for testing.';
    RAISE NOTICE 'Storage bucket "activity-images" is ready for file uploads.';
END $$;
