// =====================================================
// Email Service - Notification System
// =====================================================
// Service functions for email notifications and template management

import { supabase } from "./supabase";
import {
  getEmailSettings,
  getEmailTemplates,
  getNotificationContacts,
} from "./settings";
import type {
  BookingNotificationType,
  BookingEmailVariables,
  AdminBooking,
  BookingWithDetails,
} from "@/types/booking";
import type { EmailTemplate } from "@/types/settings";

// =====================================================
// Email Configuration
// =====================================================

interface EmailConfig {
  to: string[];
  cc?: string[];
  bcc?: string[];
  subject: string;
  html: string;
  from?: string;
  replyTo?: string;
}

interface EmailResult {
  success: boolean;
  message: string;
  messageId?: string;
}

// =====================================================
// Template Processing
// =====================================================

/**
 * Process email template with variables
 */
export function processEmailTemplate(
  template: EmailTemplate,
  variables: BookingEmailVariables
): { subject: string; html: string } {
  let subject = template.subject;
  let html = template.html_body;

  // Replace all template variables
  Object.entries(variables).forEach(([key, value]) => {
    const placeholder = `{{${key}}}`;
    const regex = new RegExp(
      placeholder.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"),
      "g"
    );

    subject = subject.replace(regex, String(value || ""));
    html = html.replace(regex, String(value || ""));
  });

  return { subject, html };
}

/**
 * Create booking email variables from booking data
 */
export function createBookingEmailVariables(
  booking: BookingWithDetails | AdminBooking,
  additionalVars: Partial<BookingEmailVariables> = {}
): BookingEmailVariables {
  const accommodation =
    "accommodation" in booking && booking.accommodation
      ? booking.accommodation
      : { name: "Unknown Accommodation" };

  const activities =
    "activities" in booking && booking.activities ? booking.activities : [];

  const totalGuests =
    booking.adults + booking.children_5_13 + booking.children_0_4;

  return {
    guest_name: booking.guest_full_name,
    booking_id: booking.id,
    check_in_date: new Date(booking.check_in).toLocaleDateString(),
    check_out_date: new Date(booking.check_out).toLocaleDateString(),
    accommodation_name: accommodation.name,
    guest_count: `${totalGuests} guest${totalGuests !== 1 ? "s" : ""} (${
      booking.adults
    } adult${booking.adults !== 1 ? "s" : ""}${
      booking.children_5_13 > 0
        ? `, ${booking.children_5_13} child${
            booking.children_5_13 !== 1 ? "ren" : ""
          } (5-13)`
        : ""
    }${
      booking.children_0_4 > 0
        ? `, ${booking.children_0_4} infant${
            booking.children_0_4 !== 1 ? "s" : ""
          } (0-4)`
        : ""
    })`,
    total_amount: booking.total_amount.toFixed(2),
    currency: booking.currency,
    status: booking.status.charAt(0).toUpperCase() + booking.status.slice(1),
    payment_status: booking.payment_status
      .replace("_", " ")
      .replace(/\b\w/g, (l) => l.toUpperCase()),
    special_requests: booking.notes_guest || "",
    ...additionalVars,
  };
}

// =====================================================
// Email Sending Functions
// =====================================================

/**
 * Send email using Supabase Edge Functions or external service
 * Note: This is a placeholder implementation. In production, you would:
 * 1. Use Supabase Edge Functions with a service like SendGrid, Mailgun, or AWS SES
 * 2. Or integrate directly with an email service provider
 */
async function sendEmail(config: EmailConfig): Promise<EmailResult> {
  try {
    // For now, we'll log the email and return success
    // In production, replace this with actual email sending logic
    console.log("📧 Email would be sent:", {
      to: config.to,
      cc: config.cc,
      bcc: config.bcc,
      subject: config.subject,
      from: config.from,
      replyTo: config.replyTo,
      htmlLength: config.html.length,
    });

    // Simulate email sending delay
    await new Promise((resolve) => setTimeout(resolve, 100));

    // TODO: Implement actual email sending
    // Example with Supabase Edge Function:
    /*
    const { data, error } = await supabase.functions.invoke('send-email', {
      body: {
        to: config.to,
        cc: config.cc,
        bcc: config.bcc,
        subject: config.subject,
        html: config.html,
        from: config.from,
        replyTo: config.replyTo
      }
    });

    if (error) throw error;
    */

    return {
      success: true,
      message: "Email sent successfully",
      messageId: `mock-${Date.now()}-${Math.random()
        .toString(36)
        .substr(2, 9)}`,
    };
  } catch (error) {
    console.error("Error sending email:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to send email",
    };
  }
}

// =====================================================
// Booking Notification Functions
// =====================================================

/**
 * Send booking confirmation email
 */
export async function sendBookingConfirmation(
  booking: BookingWithDetails | AdminBooking
): Promise<EmailResult> {
  try {
    // Get email settings and templates
    const [emailSettingsResult, templatesResult] = await Promise.all([
      getEmailSettings(),
      getEmailTemplates(),
    ]);

    if (!emailSettingsResult.success || !templatesResult.success) {
      throw new Error("Failed to load email configuration");
    }

    const emailSettings = emailSettingsResult.data;
    const templates = templatesResult.data;

    // Get notification recipients
    const recipientsResult = await getNotificationContacts("new_booking");
    if (!recipientsResult.success) {
      throw new Error("Failed to get notification recipients");
    }

    // Create email variables
    const variables = createBookingEmailVariables(booking);

    // Process template
    const { subject, html } = processEmailTemplate(
      templates.booking_confirmation,
      variables
    );

    // Send to guest
    const guestEmailResult = await sendEmail({
      to: [booking.email],
      cc: emailSettings.default_cc,
      subject,
      html,
      from: `${emailSettings.sender_name} <${emailSettings.sender_email}>`,
      replyTo: emailSettings.reply_to,
    });

    // Send to lodge staff
    const staffEmailResult = await sendEmail({
      to: recipientsResult.data,
      subject: `New Booking: ${booking.guest_full_name} - ${variables.check_in_date}`,
      html: `
        <h2>New Booking Received</h2>
        <p><strong>Guest:</strong> ${booking.guest_full_name}</p>
        <p><strong>Email:</strong> ${booking.email}</p>
        <p><strong>Phone:</strong> ${booking.phone}</p>
        <p><strong>Dates:</strong> ${variables.check_in_date} to ${
        variables.check_out_date
      }</p>
        <p><strong>Accommodation:</strong> ${variables.accommodation_name}</p>
        <p><strong>Guests:</strong> ${variables.guest_count}</p>
        <p><strong>Total:</strong> ${variables.currency} ${
        variables.total_amount
      }</p>
        <p><strong>Status:</strong> ${variables.status}</p>
        <p><strong>Payment:</strong> ${variables.payment_status}</p>
        ${
          variables.special_requests
            ? `<p><strong>Special Requests:</strong> ${variables.special_requests}</p>`
            : ""
        }
        <p><a href="${window.location.origin}/admin/bookings/${
        booking.id
      }">View Booking Details</a></p>
      `,
      from: `${emailSettings.sender_name} <${emailSettings.sender_email}>`,
      replyTo: emailSettings.reply_to,
    });

    return {
      success: guestEmailResult.success && staffEmailResult.success,
      message:
        guestEmailResult.success && staffEmailResult.success
          ? "Booking confirmation sent to guest and staff"
          : "Some emails failed to send",
      messageId: guestEmailResult.messageId,
    };
  } catch (error) {
    console.error("Error sending booking confirmation:", error);
    return {
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to send booking confirmation",
    };
  }
}

/**
 * Send booking update email
 */
export async function sendBookingUpdate(
  booking: BookingWithDetails | AdminBooking,
  updateType: "status" | "payment" | "details" = "details"
): Promise<EmailResult> {
  try {
    const [emailSettingsResult, templatesResult] = await Promise.all([
      getEmailSettings(),
      getEmailTemplates(),
    ]);

    if (!emailSettingsResult.success || !templatesResult.success) {
      throw new Error("Failed to load email configuration");
    }

    const emailSettings = emailSettingsResult.data;
    const templates = templatesResult.data;

    const variables = createBookingEmailVariables(booking);
    const { subject, html } = processEmailTemplate(
      templates.booking_update,
      variables
    );

    // Send to guest
    const result = await sendEmail({
      to: [booking.email],
      cc: emailSettings.default_cc,
      subject,
      html,
      from: `${emailSettings.sender_name} <${emailSettings.sender_email}>`,
      replyTo: emailSettings.reply_to,
    });

    return result;
  } catch (error) {
    console.error("Error sending booking update:", error);
    return {
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to send booking update",
    };
  }
}

/**
 * Send booking cancellation email
 */
export async function sendBookingCancellation(
  booking: BookingWithDetails | AdminBooking,
  cancellationReason?: string
): Promise<EmailResult> {
  try {
    const [emailSettingsResult, templatesResult] = await Promise.all([
      getEmailSettings(),
      getEmailTemplates(),
    ]);

    if (!emailSettingsResult.success || !templatesResult.success) {
      throw new Error("Failed to load email configuration");
    }

    const emailSettings = emailSettingsResult.data;
    const templates = templatesResult.data;

    const variables = createBookingEmailVariables(booking, {
      cancellation_date: new Date().toLocaleDateString(),
    });

    const { subject, html } = processEmailTemplate(
      templates.booking_cancellation,
      variables
    );

    // Get cancellation notification recipients
    const recipientsResult = await getNotificationContacts("booking_cancelled");

    // Send to guest
    const guestEmailResult = await sendEmail({
      to: [booking.email],
      cc: emailSettings.default_cc,
      subject,
      html,
      from: `${emailSettings.sender_name} <${emailSettings.sender_email}>`,
      replyTo: emailSettings.reply_to,
    });

    // Notify staff if recipients are configured
    if (recipientsResult.success && recipientsResult.data.length > 0) {
      await sendEmail({
        to: recipientsResult.data,
        subject: `Booking Cancelled: ${booking.guest_full_name} - ${variables.check_in_date}`,
        html: `
          <h2>Booking Cancelled</h2>
          <p><strong>Guest:</strong> ${booking.guest_full_name}</p>
          <p><strong>Original Dates:</strong> ${variables.check_in_date} to ${
          variables.check_out_date
        }</p>
          <p><strong>Accommodation:</strong> ${variables.accommodation_name}</p>
          <p><strong>Total Amount:</strong> ${variables.currency} ${
          variables.total_amount
        }</p>
          ${
            cancellationReason
              ? `<p><strong>Reason:</strong> ${cancellationReason}</p>`
              : ""
          }
          <p><strong>Cancelled:</strong> ${variables.cancellation_date}</p>
        `,
        from: `${emailSettings.sender_name} <${emailSettings.sender_email}>`,
        replyTo: emailSettings.reply_to,
      });
    }

    return guestEmailResult;
  } catch (error) {
    console.error("Error sending booking cancellation:", error);
    return {
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to send booking cancellation",
    };
  }
}

/**
 * Send payment received notification
 */
export async function sendPaymentReceived(
  booking: BookingWithDetails | AdminBooking,
  paymentAmount: number,
  paymentMethod: string
): Promise<EmailResult> {
  try {
    const emailSettingsResult = await getEmailSettings();
    if (!emailSettingsResult.success) {
      throw new Error("Failed to load email configuration");
    }

    const emailSettings = emailSettingsResult.data;
    const variables = createBookingEmailVariables(booking);

    // Send payment confirmation to guest
    const result = await sendEmail({
      to: [booking.email],
      cc: emailSettings.default_cc,
      subject: `Payment Received - Booking ${booking.id}`,
      html: `
        <h2>Payment Received</h2>
        <p>Dear ${booking.guest_full_name},</p>
        <p>We have received your payment for booking ${booking.id}.</p>
        <h3>Payment Details</h3>
        <ul>
          <li><strong>Amount:</strong> ${
            booking.currency
          } ${paymentAmount.toFixed(2)}</li>
          <li><strong>Method:</strong> ${paymentMethod}</li>
          <li><strong>Date:</strong> ${new Date().toLocaleDateString()}</li>
        </ul>
        <h3>Booking Details</h3>
        <ul>
          <li><strong>Check-in:</strong> ${variables.check_in_date}</li>
          <li><strong>Check-out:</strong> ${variables.check_out_date}</li>
          <li><strong>Accommodation:</strong> ${
            variables.accommodation_name
          }</li>
          <li><strong>Guests:</strong> ${variables.guest_count}</li>
        </ul>
        <p>Thank you for your payment. We look forward to welcoming you!</p>
        <p>Best regards,<br>The Malombo Team</p>
      `,
      from: `${emailSettings.sender_name} <${emailSettings.sender_email}>`,
      replyTo: emailSettings.reply_to,
    });

    // Notify staff
    const recipientsResult = await getNotificationContacts("payment_received");
    if (recipientsResult.success && recipientsResult.data.length > 0) {
      await sendEmail({
        to: recipientsResult.data,
        subject: `Payment Received: ${booking.guest_full_name} - ${
          booking.currency
        } ${paymentAmount.toFixed(2)}`,
        html: `
          <h2>Payment Received</h2>
          <p><strong>Guest:</strong> ${booking.guest_full_name}</p>
          <p><strong>Booking ID:</strong> ${booking.id}</p>
          <p><strong>Amount:</strong> ${
            booking.currency
          } ${paymentAmount.toFixed(2)}</p>
          <p><strong>Method:</strong> ${paymentMethod}</p>
          <p><strong>Payment Status:</strong> ${variables.payment_status}</p>
          <p><a href="${window.location.origin}/admin/bookings/${
          booking.id
        }">View Booking Details</a></p>
        `,
        from: `${emailSettings.sender_name} <${emailSettings.sender_email}>`,
        replyTo: emailSettings.reply_to,
      });
    }

    return result;
  } catch (error) {
    console.error("Error sending payment notification:", error);
    return {
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to send payment notification",
    };
  }
}

// =====================================================
// Utility Functions
// =====================================================

/**
 * Send custom email
 */
export async function sendCustomEmail(
  to: string[],
  subject: string,
  html: string,
  options: {
    cc?: string[];
    bcc?: string[];
    replyTo?: string;
  } = {}
): Promise<EmailResult> {
  try {
    const emailSettingsResult = await getEmailSettings();
    if (!emailSettingsResult.success) {
      throw new Error("Failed to load email configuration");
    }

    const emailSettings = emailSettingsResult.data;

    return await sendEmail({
      to,
      cc: options.cc || emailSettings.default_cc,
      bcc: options.bcc || emailSettings.default_bcc,
      subject,
      html,
      from: `${emailSettings.sender_name} <${emailSettings.sender_email}>`,
      replyTo: options.replyTo || emailSettings.reply_to,
    });
  } catch (error) {
    console.error("Error sending custom email:", error);
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to send email",
    };
  }
}

/**
 * Test email configuration
 */
export async function testEmailConfiguration(
  testEmail: string
): Promise<EmailResult> {
  try {
    const result = await sendCustomEmail(
      [testEmail],
      "Malombo Admin Panel - Email Test",
      `
        <h2>Email Configuration Test</h2>
        <p>This is a test email from the Malombo Admin Panel.</p>
        <p>If you received this email, your email configuration is working correctly.</p>
        <p><strong>Sent at:</strong> ${new Date().toLocaleString()}</p>
        <p>Best regards,<br>Malombo Admin System</p>
      `
    );

    return result;
  } catch (error) {
    console.error("Error testing email configuration:", error);
    return {
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to test email configuration",
    };
  }
}

/**
 * Validate email template
 */
export function validateEmailTemplate(template: EmailTemplate): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!template.subject || template.subject.trim() === "") {
    errors.push("Subject is required");
  }

  if (!template.html_body || template.html_body.trim() === "") {
    errors.push("HTML body is required");
  }

  // Check for basic HTML structure
  if (template.html_body && !template.html_body.includes("<html>")) {
    errors.push("HTML body should contain proper HTML structure");
  }

  // Check for required placeholders in booking templates
  const requiredPlaceholders = ["{{guest_name}}", "{{booking_id}}"];
  requiredPlaceholders.forEach((placeholder) => {
    if (
      !template.subject.includes(placeholder) &&
      !template.html_body.includes(placeholder)
    ) {
      // This is a warning, not an error
    }
  });

  return {
    valid: errors.length === 0,
    errors,
  };
}
