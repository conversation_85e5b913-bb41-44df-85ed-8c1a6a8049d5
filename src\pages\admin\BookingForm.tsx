// =====================================================
// Create/Edit Booking Form - Comprehensive Form
// =====================================================
// Booking creation and editing form with validation and pricing

import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";
import {
  ArrowLeft,
  Save,
  Calculator,
  AlertCircle,
  CheckCircle,
  Users,
  Calendar,
  MapPin,
  Activity,
  DollarSign,
  Mail,
  Phone,
} from "lucide-react";

// Import types and services
import type {
  BookingCreateRequest,
  BookingUpdateRequest,
  BookingFormState,
  BookingValidationError,
  Currency,
  BookingSource,
  PricingCalculationRequest,
} from "@/types/booking";
import { CURRENCY_CONFIG } from "@/types/booking";
import {
  createBooking,
  updateBooking,
  getBooking,
  calculateBookingPricing,
  checkAvailability,
} from "@/lib/bookings";
import { getAccommodations } from "@/lib/accommodations";
import { getActivities } from "@/lib/activities";

interface Accommodation {
  id: string;
  name: string;
  type: string;
  capacity: number;
  status: string;
}

interface Activity {
  id: string;
  title: string;
  category: string;
  status: string;
  pricing: string;
}

export default function BookingForm() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEditing = id && id !== "new";

  // State management
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [accommodations, setAccommodations] = useState<Accommodation[]>([]);
  const [activities, setActivities] = useState<Activity[]>([]);
  const [availabilityChecking, setAvailabilityChecking] = useState(false);
  const [availabilityResult, setAvailabilityResult] = useState<{
    available: boolean;
    message: string;
  } | null>(null);

  // Form state
  const [formData, setFormData] = useState<BookingFormState>({
    guest: {
      guest_full_name: "",
      email: "",
      phone: "",
      country: "",
    },
    party: {
      adults: 1,
      children_5_13: 0,
      children_0_4: 0,
    },
    stay: {
      accommodation_id: "",
      check_in: "",
      check_out: "",
    },
    activities: [],
    pricing: {
      currency: "USD",
      base_amount: 0,
      discounts: 0,
      taxes: 0,
    },
    payment: {
      payment_status: "unpaid",
      payment_method: "",
      payment_reference: "",
    },
    meta: {
      source: "website",
      notes_admin: "",
      notes_guest: "",
    },
    errors: [],
    isSubmitting: false,
    isDirty: false,
  });

  // Calculated values
  const [calculatedPricing, setCalculatedPricing] = useState({
    subtotal: 0,
    taxes: 0,
    total: 0,
    breakdown: [] as Array<{
      item: string;
      quantity: number;
      unit_price: number;
      total_price: number;
    }>,
  });

  // Load initial data
  useEffect(() => {
    loadInitialData();
  }, []);

  // Load booking data for editing
  useEffect(() => {
    if (isEditing && id) {
      loadBookingData(id);
    }
  }, [isEditing, id]);

  // Calculate pricing when relevant fields change
  useEffect(() => {
    if (
      formData.stay.accommodation_id &&
      formData.stay.check_in &&
      formData.stay.check_out &&
      formData.party.adults > 0
    ) {
      calculatePricing();
    }
  }, [
    formData.stay.accommodation_id,
    formData.stay.check_in,
    formData.stay.check_out,
    formData.party,
    formData.activities,
    formData.pricing.currency,
  ]);

  // Check availability when dates or accommodation change
  useEffect(() => {
    if (
      formData.stay.accommodation_id &&
      formData.stay.check_in &&
      formData.stay.check_out
    ) {
      checkBookingAvailability();
    }
  }, [
    formData.stay.accommodation_id,
    formData.stay.check_in,
    formData.stay.check_out,
  ]);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      const [accommodationsResponse, activitiesResponse] = await Promise.all([
        getAccommodations({ status: "published" }),
        getActivities({ status: "published" }),
      ]);

      if (accommodationsResponse.success) {
        setAccommodations(accommodationsResponse.data);
      }

      if (activitiesResponse.success) {
        setActivities(activitiesResponse.data);
      }
    } catch (error) {
      console.error("Error loading initial data:", error);
      toast.error("Failed to load form data");
    } finally {
      setLoading(false);
    }
  };

  const loadBookingData = async (bookingId: string) => {
    try {
      setLoading(true);
      const response = await getBooking(bookingId);

      if (response.success) {
        const booking = response.data;
        setFormData({
          guest: {
            guest_full_name: booking.guest_full_name,
            email: booking.email,
            phone: booking.phone,
            country: booking.country,
          },
          party: {
            adults: booking.adults,
            children_5_13: booking.children_5_13,
            children_0_4: booking.children_0_4,
          },
          stay: {
            accommodation_id: booking.accommodation_id,
            check_in: booking.check_in,
            check_out: booking.check_out,
          },
          activities: booking.activity_ids || [],
          pricing: {
            currency: booking.currency,
            base_amount: booking.base_amount,
            discounts: booking.discounts,
            taxes: booking.taxes,
          },
          payment: {
            payment_status: booking.payment_status,
            payment_method: booking.payment_method || "",
            payment_reference: booking.payment_reference || "",
          },
          meta: {
            source: booking.source,
            notes_admin: booking.notes_admin || "",
            notes_guest: booking.notes_guest || "",
          },
          errors: [],
          isSubmitting: false,
          isDirty: false,
        });
      } else {
        toast.error(response.message || "Failed to load booking");
        navigate("/admin/bookings");
      }
    } catch (error) {
      console.error("Error loading booking:", error);
      toast.error("Failed to load booking");
      navigate("/admin/bookings");
    } finally {
      setLoading(false);
    }
  };

  const calculatePricing = async () => {
    try {
      const request: PricingCalculationRequest = {
        accommodation_id: formData.stay.accommodation_id,
        check_in: formData.stay.check_in,
        check_out: formData.stay.check_out,
        party: formData.party,
        activity_ids: formData.activities,
        currency: formData.pricing.currency,
      };

      const response = await calculateBookingPricing(request);
      if (response.success) {
        setCalculatedPricing({
          subtotal: response.data.subtotal,
          taxes: response.data.taxes,
          total: response.data.total,
          breakdown: response.data.breakdown,
        });

        // Update form pricing
        setFormData((prev) => ({
          ...prev,
          pricing: {
            ...prev.pricing,
            base_amount: response.data.subtotal,
            taxes: response.data.taxes,
          },
          isDirty: true,
        }));
      }
    } catch (error) {
      console.error("Error calculating pricing:", error);
    }
  };

  const checkBookingAvailability = async () => {
    try {
      setAvailabilityChecking(true);
      const response = await checkAvailability({
        accommodation_id: formData.stay.accommodation_id,
        check_in: formData.stay.check_in,
        check_out: formData.stay.check_out,
        guests: formData.party,
      });

      if (response.success) {
        setAvailabilityResult({
          available: response.data.available,
          message: response.data.available
            ? "Accommodation is available for selected dates"
            : `Accommodation is not available. ${
                response.data.conflicts?.length || 0
              } conflicting booking(s) found.`,
        });
      }
    } catch (error) {
      console.error("Error checking availability:", error);
    } finally {
      setAvailabilityChecking(false);
    }
  };

  // Form validation
  const validateForm = (): BookingValidationError[] => {
    const errors: BookingValidationError[] = [];

    // Guest validation
    if (!formData.guest.guest_full_name.trim()) {
      errors.push({
        field: "guest_full_name",
        message: "Guest name is required",
        code: "required",
      });
    }

    if (!formData.guest.email.trim()) {
      errors.push({
        field: "email",
        message: "Email is required",
        code: "required",
      });
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.guest.email)) {
      errors.push({
        field: "email",
        message: "Please enter a valid email address",
        code: "invalid",
      });
    }

    if (!formData.guest.phone.trim()) {
      errors.push({
        field: "phone",
        message: "Phone number is required",
        code: "required",
      });
    }

    if (!formData.guest.country.trim()) {
      errors.push({
        field: "country",
        message: "Country is required",
        code: "required",
      });
    }

    // Party validation
    if (formData.party.adults < 1) {
      errors.push({
        field: "adults",
        message: "At least one adult is required",
        code: "min_value",
      });
    }

    // Stay validation
    if (!formData.stay.accommodation_id) {
      errors.push({
        field: "accommodation_id",
        message: "Please select an accommodation",
        code: "required",
      });
    }

    if (!formData.stay.check_in) {
      errors.push({
        field: "check_in",
        message: "Check-in date is required",
        code: "required",
      });
    }

    if (!formData.stay.check_out) {
      errors.push({
        field: "check_out",
        message: "Check-out date is required",
        code: "required",
      });
    }

    if (formData.stay.check_in && formData.stay.check_out) {
      const checkIn = new Date(formData.stay.check_in);
      const checkOut = new Date(formData.stay.check_out);

      if (checkOut <= checkIn) {
        errors.push({
          field: "check_out",
          message: "Check-out date must be after check-in date",
          code: "invalid_range",
        });
      }
    }

    // Currency validation
    if (!formData.pricing.currency) {
      errors.push({
        field: "currency",
        message: "Currency is required",
        code: "required",
      });
    }

    return errors;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const errors = validateForm();
    if (errors.length > 0) {
      setFormData((prev) => ({ ...prev, errors }));
      toast.error("Please fix the form errors before submitting");
      return;
    }

    try {
      setSaving(true);
      setFormData((prev) => ({ ...prev, isSubmitting: true, errors: [] }));

      const bookingData = {
        ...formData.guest,
        ...formData.party,
        ...formData.stay,
        activity_ids: formData.activities,
        ...formData.pricing,
        total_amount: calculatedPricing.total,
        ...formData.payment,
        ...formData.meta,
      };

      let response;
      if (isEditing && id) {
        response = await updateBooking({ id, ...bookingData });
      } else {
        response = await createBooking(bookingData);
      }

      if (response.success) {
        toast.success(
          isEditing
            ? "Booking updated successfully"
            : "Booking created successfully"
        );
        navigate(`/admin/bookings/${response.data.id}`);
      } else {
        toast.error(response.message || "Failed to save booking");
      }
    } catch (error) {
      console.error("Error saving booking:", error);
      toast.error("Failed to save booking");
    } finally {
      setSaving(false);
      setFormData((prev) => ({ ...prev, isSubmitting: false }));
    }
  };

  // Handle form field changes
  const updateField = (
    section: keyof BookingFormState,
    field: string,
    value: any
  ) => {
    setFormData((prev) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
      isDirty: true,
      errors: prev.errors.filter((error) => error.field !== field),
    }));
  };

  // Handle activity selection
  const toggleActivity = (activityId: string) => {
    setFormData((prev) => ({
      ...prev,
      activities: prev.activities.includes(activityId)
        ? prev.activities.filter((id) => id !== activityId)
        : [...prev.activities, activityId],
      isDirty: true,
    }));
  };

  // Get field error
  const getFieldError = (fieldName: string) => {
    return formData.errors.find((error) => error.field === fieldName);
  };

  // Calculate nights
  const calculateNights = () => {
    if (formData.stay.check_in && formData.stay.check_out) {
      const checkIn = new Date(formData.stay.check_in);
      const checkOut = new Date(formData.stay.check_out);
      const diffTime = checkOut.getTime() - checkIn.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      return diffDays > 0 ? diffDays : 0;
    }
    return 0;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading form...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => navigate("/admin/bookings")}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Bookings
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {isEditing ? "Edit Booking" : "Create New Booking"}
            </h1>
            <p className="text-gray-600 mt-1">
              {isEditing
                ? `Editing booking ${id}`
                : "Create a new guest reservation"}
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Guest Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="w-5 h-5 mr-2" />
                  Guest Information
                </CardTitle>
                <CardDescription>
                  Enter the primary guest's contact details
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="guest_full_name">
                    Full Name <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="guest_full_name"
                    value={formData.guest.guest_full_name}
                    onChange={(e) =>
                      updateField("guest", "guest_full_name", e.target.value)
                    }
                    placeholder="Enter guest's full name"
                    className={
                      getFieldError("guest_full_name") ? "border-red-500" : ""
                    }
                  />
                  {getFieldError("guest_full_name") && (
                    <p className="text-sm text-red-500 mt-1 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {getFieldError("guest_full_name")?.message}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="email">
                      Email Address <span className="text-red-500">*</span>
                    </Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Input
                        id="email"
                        type="email"
                        value={formData.guest.email}
                        onChange={(e) =>
                          updateField("guest", "email", e.target.value)
                        }
                        placeholder="<EMAIL>"
                        className={`pl-10 ${
                          getFieldError("email") ? "border-red-500" : ""
                        }`}
                      />
                    </div>
                    {getFieldError("email") && (
                      <p className="text-sm text-red-500 mt-1 flex items-center">
                        <AlertCircle className="w-4 h-4 mr-1" />
                        {getFieldError("email")?.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="phone">
                      Phone Number <span className="text-red-500">*</span>
                    </Label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <Input
                        id="phone"
                        value={formData.guest.phone}
                        onChange={(e) =>
                          updateField("guest", "phone", e.target.value)
                        }
                        placeholder="****** 567 8900"
                        className={`pl-10 ${
                          getFieldError("phone") ? "border-red-500" : ""
                        }`}
                      />
                    </div>
                    {getFieldError("phone") && (
                      <p className="text-sm text-red-500 mt-1 flex items-center">
                        <AlertCircle className="w-4 h-4 mr-1" />
                        {getFieldError("phone")?.message}
                      </p>
                    )}
                  </div>
                </div>

                <div>
                  <Label htmlFor="country">
                    Country <span className="text-red-500">*</span>
                  </Label>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      id="country"
                      value={formData.guest.country}
                      onChange={(e) =>
                        updateField("guest", "country", e.target.value)
                      }
                      placeholder="Enter country"
                      className={`pl-10 ${
                        getFieldError("country") ? "border-red-500" : ""
                      }`}
                    />
                  </div>
                  {getFieldError("country") && (
                    <p className="text-sm text-red-500 mt-1 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {getFieldError("country")?.message}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Party Composition */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="w-5 h-5 mr-2" />
                  Party Composition
                </CardTitle>
                <CardDescription>
                  Specify the number of guests in each age group
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="adults">
                      Adults <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="adults"
                      type="number"
                      min="1"
                      value={formData.party.adults}
                      onChange={(e) =>
                        updateField(
                          "party",
                          "adults",
                          parseInt(e.target.value) || 1
                        )
                      }
                      className={
                        getFieldError("adults") ? "border-red-500" : ""
                      }
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      14+ years (full price)
                    </p>
                    {getFieldError("adults") && (
                      <p className="text-sm text-red-500 mt-1 flex items-center">
                        <AlertCircle className="w-4 h-4 mr-1" />
                        {getFieldError("adults")?.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="children_5_13">Children (5-13)</Label>
                    <Input
                      id="children_5_13"
                      type="number"
                      min="0"
                      value={formData.party.children_5_13}
                      onChange={(e) =>
                        updateField(
                          "party",
                          "children_5_13",
                          parseInt(e.target.value) || 0
                        )
                      }
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      5-13 years (50% price)
                    </p>
                  </div>

                  <div>
                    <Label htmlFor="children_0_4">Infants (0-4)</Label>
                    <Input
                      id="children_0_4"
                      type="number"
                      min="0"
                      value={formData.party.children_0_4}
                      onChange={(e) =>
                        updateField(
                          "party",
                          "children_0_4",
                          parseInt(e.target.value) || 0
                        )
                      }
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      0-4 years (free)
                    </p>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <strong>Total Guests:</strong>{" "}
                    {formData.party.adults +
                      formData.party.children_5_13 +
                      formData.party.children_0_4}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Stay Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="w-5 h-5 mr-2" />
                  Stay Details
                </CardTitle>
                <CardDescription>
                  Select accommodation and dates
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="accommodation_id">
                    Accommodation <span className="text-red-500">*</span>
                  </Label>
                  <Select
                    value={formData.stay.accommodation_id}
                    onValueChange={(value) =>
                      updateField("stay", "accommodation_id", value)
                    }
                  >
                    <SelectTrigger
                      className={
                        getFieldError("accommodation_id")
                          ? "border-red-500"
                          : ""
                      }
                    >
                      <SelectValue placeholder="Select accommodation" />
                    </SelectTrigger>
                    <SelectContent>
                      {accommodations.map((accommodation) => (
                        <SelectItem
                          key={accommodation.id}
                          value={accommodation.id}
                        >
                          {accommodation.name} ({accommodation.type})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {getFieldError("accommodation_id") && (
                    <p className="text-sm text-red-500 mt-1 flex items-center">
                      <AlertCircle className="w-4 h-4 mr-1" />
                      {getFieldError("accommodation_id")?.message}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="check_in">
                      Check-in Date <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="check_in"
                      type="date"
                      value={formData.stay.check_in}
                      onChange={(e) =>
                        updateField("stay", "check_in", e.target.value)
                      }
                      min={new Date().toISOString().split("T")[0]}
                      className={
                        getFieldError("check_in") ? "border-red-500" : ""
                      }
                    />
                    {getFieldError("check_in") && (
                      <p className="text-sm text-red-500 mt-1 flex items-center">
                        <AlertCircle className="w-4 h-4 mr-1" />
                        {getFieldError("check_in")?.message}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="check_out">
                      Check-out Date <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="check_out"
                      type="date"
                      value={formData.stay.check_out}
                      onChange={(e) =>
                        updateField("stay", "check_out", e.target.value)
                      }
                      min={
                        formData.stay.check_in ||
                        new Date().toISOString().split("T")[0]
                      }
                      className={
                        getFieldError("check_out") ? "border-red-500" : ""
                      }
                    />
                    {getFieldError("check_out") && (
                      <p className="text-sm text-red-500 mt-1 flex items-center">
                        <AlertCircle className="w-4 h-4 mr-1" />
                        {getFieldError("check_out")?.message}
                      </p>
                    )}
                  </div>
                </div>

                {calculateNights() > 0 && (
                  <div className="p-3 bg-green-50 rounded-lg">
                    <p className="text-sm text-green-800">
                      <strong>Duration:</strong> {calculateNights()} night
                      {calculateNights() !== 1 ? "s" : ""}
                    </p>
                  </div>
                )}

                {/* Availability Check */}
                {availabilityChecking && (
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <p className="text-sm text-blue-800">
                      Checking availability...
                    </p>
                  </div>
                )}

                {availabilityResult && (
                  <div
                    className={`p-3 rounded-lg ${
                      availabilityResult.available ? "bg-green-50" : "bg-red-50"
                    }`}
                  >
                    <p
                      className={`text-sm flex items-center ${
                        availabilityResult.available
                          ? "text-green-800"
                          : "text-red-800"
                      }`}
                    >
                      {availabilityResult.available ? (
                        <CheckCircle className="w-4 h-4 mr-2" />
                      ) : (
                        <AlertCircle className="w-4 h-4 mr-2" />
                      )}
                      {availabilityResult.message}
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Activities */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Activity className="w-5 h-5 mr-2" />
                  Activities
                </CardTitle>
                <CardDescription>
                  Select optional activities for the stay
                </CardDescription>
              </CardHeader>
              <CardContent>
                {activities.length > 0 ? (
                  <div className="space-y-3">
                    {activities.map((activity) => (
                      <div
                        key={activity.id}
                        className="flex items-center space-x-3 p-3 border rounded-lg"
                      >
                        <Checkbox
                          id={activity.id}
                          checked={formData.activities.includes(activity.id)}
                          onCheckedChange={() => toggleActivity(activity.id)}
                        />
                        <div className="flex-1">
                          <Label
                            htmlFor={activity.id}
                            className="font-medium cursor-pointer"
                          >
                            {activity.title}
                          </Label>
                          <p className="text-sm text-gray-600">
                            {activity.category}
                          </p>
                          {activity.pricing && (
                            <p className="text-sm text-amber-600 font-medium">
                              {activity.pricing}
                            </p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Activity className="w-12 h-12 mx-auto mb-2 opacity-50" />
                    <p>No activities available</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Additional Information */}
            <Card>
              <CardHeader>
                <CardTitle>Additional Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="source">Booking Source</Label>
                    <Select
                      value={formData.meta.source}
                      onValueChange={(value) =>
                        updateField("meta", "source", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="website">Website</SelectItem>
                        <SelectItem value="email">Email</SelectItem>
                        <SelectItem value="whatsapp">WhatsApp</SelectItem>
                        <SelectItem value="agent">Travel Agent</SelectItem>
                        <SelectItem value="phone">Phone</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="currency">Currency</Label>
                    <Select
                      value={formData.pricing.currency}
                      onValueChange={(value) =>
                        updateField("pricing", "currency", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.entries(CURRENCY_CONFIG).map(
                          ([code, config]) => (
                            <SelectItem key={code} value={code}>
                              {config.symbol} {config.name} ({code})
                            </SelectItem>
                          )
                        )}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="notes_guest">Guest Notes</Label>
                  <Textarea
                    id="notes_guest"
                    value={formData.meta.notes_guest}
                    onChange={(e) =>
                      updateField("meta", "notes_guest", e.target.value)
                    }
                    placeholder="Special requests, dietary requirements, etc."
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="notes_admin">Admin Notes</Label>
                  <Textarea
                    id="notes_admin"
                    value={formData.meta.notes_admin}
                    onChange={(e) =>
                      updateField("meta", "notes_admin", e.target.value)
                    }
                    placeholder="Internal notes for staff"
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Pricing Sidebar */}
          <div className="space-y-6">
            {/* Pricing Summary */}
            <Card className="sticky top-6">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <DollarSign className="w-5 h-5 mr-2" />
                  Pricing Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {calculatedPricing.breakdown.length > 0 && (
                  <div className="space-y-3">
                    <h4 className="font-medium text-sm text-gray-700">
                      Breakdown
                    </h4>
                    {calculatedPricing.breakdown.map((item, index) => (
                      <div key={index} className="flex justify-between text-sm">
                        <span className="text-gray-600">
                          {item.item}{" "}
                          {item.quantity > 1 && `(×${item.quantity})`}
                        </span>
                        <span className="font-medium">
                          {CURRENCY_CONFIG[formData.pricing.currency]?.symbol}
                          {item.total_price.toFixed(
                            CURRENCY_CONFIG[formData.pricing.currency]
                              ?.decimals || 2
                          )}
                        </span>
                      </div>
                    ))}
                    <Separator />
                  </div>
                )}

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Subtotal</span>
                    <span className="font-medium">
                      {CURRENCY_CONFIG[formData.pricing.currency]?.symbol}
                      {calculatedPricing.subtotal.toFixed(
                        CURRENCY_CONFIG[formData.pricing.currency]?.decimals ||
                          2
                      )}
                    </span>
                  </div>

                  {formData.pricing.discounts > 0 && (
                    <div className="flex justify-between text-green-600">
                      <span>Discounts</span>
                      <span>
                        -{CURRENCY_CONFIG[formData.pricing.currency]?.symbol}
                        {formData.pricing.discounts.toFixed(
                          CURRENCY_CONFIG[formData.pricing.currency]
                            ?.decimals || 2
                        )}
                      </span>
                    </div>
                  )}

                  {calculatedPricing.taxes > 0 && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Taxes</span>
                      <span className="font-medium">
                        {CURRENCY_CONFIG[formData.pricing.currency]?.symbol}
                        {calculatedPricing.taxes.toFixed(
                          CURRENCY_CONFIG[formData.pricing.currency]
                            ?.decimals || 2
                        )}
                      </span>
                    </div>
                  )}
                </div>

                <Separator />

                <div className="flex justify-between text-lg font-bold">
                  <span>Total</span>
                  <span className="text-amber-600">
                    {CURRENCY_CONFIG[formData.pricing.currency]?.symbol}
                    {calculatedPricing.total.toFixed(
                      CURRENCY_CONFIG[formData.pricing.currency]?.decimals || 2
                    )}
                  </span>
                </div>

                <Button
                  type="button"
                  variant="outline"
                  onClick={calculatePricing}
                  className="w-full"
                  disabled={
                    !formData.stay.accommodation_id ||
                    !formData.stay.check_in ||
                    !formData.stay.check_out
                  }
                >
                  <Calculator className="w-4 h-4 mr-2" />
                  Recalculate
                </Button>
              </CardContent>
            </Card>

            {/* Payment Information */}
            <Card>
              <CardHeader>
                <CardTitle>Payment Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="payment_status">Payment Status</Label>
                  <Select
                    value={formData.payment.payment_status}
                    onValueChange={(value) =>
                      updateField("payment", "payment_status", value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="unpaid">Unpaid</SelectItem>
                      <SelectItem value="deposit_paid">Deposit Paid</SelectItem>
                      <SelectItem value="paid">Paid</SelectItem>
                      <SelectItem value="refunded">Refunded</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="payment_method">Payment Method</Label>
                  <Input
                    id="payment_method"
                    value={formData.payment.payment_method}
                    onChange={(e) =>
                      updateField("payment", "payment_method", e.target.value)
                    }
                    placeholder="e.g., Credit Card, Bank Transfer"
                  />
                </div>

                <div>
                  <Label htmlFor="payment_reference">Payment Reference</Label>
                  <Input
                    id="payment_reference"
                    value={formData.payment.payment_reference}
                    onChange={(e) =>
                      updateField(
                        "payment",
                        "payment_reference",
                        e.target.value
                      )
                    }
                    placeholder="Transaction ID, Check number, etc."
                  />
                </div>
              </CardContent>
            </Card>

            {/* Form Actions */}
            <Card>
              <CardContent className="p-4">
                <div className="space-y-3">
                  <Button
                    type="submit"
                    className="w-full bg-amber-600 hover:bg-amber-700"
                    disabled={saving || formData.isSubmitting}
                  >
                    <Save className="w-4 h-4 mr-2" />
                    {saving
                      ? "Saving..."
                      : isEditing
                      ? "Update Booking"
                      : "Create Booking"}
                  </Button>

                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate("/admin/bookings")}
                    className="w-full"
                    disabled={saving || formData.isSubmitting}
                  >
                    Cancel
                  </Button>
                </div>

                {formData.isDirty && (
                  <p className="text-xs text-amber-600 mt-2 text-center">
                    You have unsaved changes
                  </p>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  );
}
