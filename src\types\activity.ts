// =====================================================
// Activity Types and Interfaces
// =====================================================
// Comprehensive TypeScript definitions for activity management

// Activity category enum
export type ActivityCategory = 
  | 'game_drive' 
  | 'boat_safari' 
  | 'walk_safari' 
  | 'cultural_tour' 
  | 'fishing' 
  | 'village_tour' 
  | 'youth_program';

// Activity status enum
export type ActivityStatus = 'draft' | 'published' | 'unpublished';

// Main activity interface (matches database schema)
export interface Activity {
  id: string;
  title: string;
  category: ActivityCategory;
  description: string;
  duration: string;
  schedule?: string;
  pricing: string;
  inclusions: string[];
  images: string[];
  status: ActivityStatus;
  featured: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
}

// Activity form data interface (for create/edit forms)
export interface ActivityFormData {
  title: string;
  category: ActivityCategory;
  description: string;
  duration: string;
  schedule?: string;
  pricing: string;
  inclusions: string[];
  images: File[]; // New images to upload
  existing_images: string[]; // Existing image URLs
  status: ActivityStatus;
  featured: boolean;
}

// Activity creation request (for API)
export interface ActivityCreateRequest {
  title: string;
  category: ActivityCategory;
  description: string;
  duration: string;
  schedule?: string;
  pricing: string;
  inclusions: string[];
  images: string[];
  status: ActivityStatus;
  featured: boolean;
}

// Activity update request (for API)
export interface ActivityUpdateRequest extends Partial<ActivityCreateRequest> {
  id: string;
}

// Activity filters for list view
export interface ActivityFilters {
  search: string;
  category: ActivityCategory | 'all';
  status: ActivityStatus | 'all';
  featured: boolean | 'all';
}

// Activity sorting options
export interface ActivitySort {
  field: 'title' | 'category' | 'pricing' | 'created_at' | 'status' | 'featured';
  direction: 'asc' | 'desc';
}

// Activity list response with pagination
export interface ActivityListResponse {
  data: Activity[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  filters: ActivityFilters;
  sort: ActivitySort;
}

// Single activity response
export interface ActivityResponse {
  data: Activity;
  success: boolean;
  message?: string;
}

// Image upload response
export interface ActivityImageUploadResponse {
  url: string;
  path: string;
  success: boolean;
  message?: string;
}

// Bulk operation types
export interface BulkActivityOperation {
  action: 'publish' | 'unpublish' | 'feature' | 'unfeature' | 'delete';
  activity_ids: string[];
}

export interface BulkActivityOperationResponse {
  success: boolean;
  updated_count: number;
  failed_count: number;
  message: string;
}

// Activity statistics
export interface ActivityStats {
  total_activities: number;
  published_activities: number;
  draft_activities: number;
  featured_activities: number;
  activities_by_category: Record<ActivityCategory, number>;
}

// Predefined activity categories with labels
export const ACTIVITY_CATEGORY_LABELS: Record<ActivityCategory, string> = {
  game_drive: 'Game Drive',
  boat_safari: 'Boat Safari',
  walk_safari: 'Walking Safari',
  cultural_tour: 'Cultural Tour',
  fishing: 'Fishing',
  village_tour: 'Village Tour',
  youth_program: 'Youth Program'
};

// Predefined inclusions list for form dropdowns
export const ACTIVITY_INCLUSIONS = [
  'Professional guide',
  'Armed ranger',
  'Boat captain',
  '4WD safari vehicle',
  'Boat transport',
  'Life jackets',
  'Binoculars',
  'Photography assistance',
  'Bottled water',
  'Refreshments',
  'Snacks',
  'Picnic lunch',
  'Traditional lunch',
  'Sunset drinks',
  'Park fees',
  'Village entry fees',
  'Fishing equipment',
  'Bait',
  'First aid kit',
  'Cultural demonstrations',
  'Traditional crafts workshop',
  'Local interpreter',
  'Transportation',
  'Insurance coverage'
] as const;

// Activity status display configuration
export const ACTIVITY_STATUS_CONFIG: Record<ActivityStatus, {
  label: string;
  color: string;
  bgColor: string;
}> = {
  draft: {
    label: 'Draft',
    color: 'text-gray-600',
    bgColor: 'bg-gray-100'
  },
  published: {
    label: 'Published',
    color: 'text-green-700',
    bgColor: 'bg-green-100'
  },
  unpublished: {
    label: 'Unpublished',
    color: 'text-red-700',
    bgColor: 'bg-red-100'
  }
};

// Activity category colors for UI
export const ACTIVITY_CATEGORY_COLORS: Record<ActivityCategory, {
  bg: string;
  text: string;
  border: string;
}> = {
  game_drive: {
    bg: 'bg-amber-50',
    text: 'text-amber-700',
    border: 'border-amber-200'
  },
  boat_safari: {
    bg: 'bg-blue-50',
    text: 'text-blue-700',
    border: 'border-blue-200'
  },
  walk_safari: {
    bg: 'bg-green-50',
    text: 'text-green-700',
    border: 'border-green-200'
  },
  cultural_tour: {
    bg: 'bg-purple-50',
    text: 'text-purple-700',
    border: 'border-purple-200'
  },
  fishing: {
    bg: 'bg-cyan-50',
    text: 'text-cyan-700',
    border: 'border-cyan-200'
  },
  village_tour: {
    bg: 'bg-orange-50',
    text: 'text-orange-700',
    border: 'border-orange-200'
  },
  youth_program: {
    bg: 'bg-pink-50',
    text: 'text-pink-700',
    border: 'border-pink-200'
  }
};

// Form validation schemas (for use with react-hook-form)
export interface ActivityFormErrors {
  title?: string;
  category?: string;
  description?: string;
  duration?: string;
  pricing?: string;
  images?: string;
  inclusions?: string;
  general?: string;
}

// Default form values
export const DEFAULT_ACTIVITY_FORM: ActivityFormData = {
  title: '',
  category: 'game_drive',
  description: '',
  duration: '',
  schedule: '',
  pricing: '',
  inclusions: [],
  images: [],
  existing_images: [],
  status: 'draft',
  featured: false
};

// Default filters
export const DEFAULT_ACTIVITY_FILTERS: ActivityFilters = {
  search: '',
  category: 'all',
  status: 'all',
  featured: 'all'
};

// Default sorting
export const DEFAULT_ACTIVITY_SORT: ActivitySort = {
  field: 'created_at',
  direction: 'desc'
};

// Schedule options for form
export const ACTIVITY_SCHEDULE_OPTIONS = [
  'Morning (6AM–9AM)',
  'Morning (6AM–10AM)',
  'Morning (7AM–9AM)',
  'Morning (8AM–11AM)',
  'Afternoon (2PM–5PM)',
  'Afternoon (2PM–6PM)',
  'Afternoon (3PM–6PM)',
  'Evening (4PM–6:30PM)',
  'Evening (5PM–7PM)',
  'Full Day (6AM–6PM)',
  'Half Day (6AM–12PM)',
  'Half Day (12PM–6PM)',
  'Multi-day',
  'Flexible timing'
] as const;

// Duration options for form
export const ACTIVITY_DURATION_OPTIONS = [
  '1 hour',
  '1.5 hours',
  '2 hours',
  '2.5 hours',
  '3 hours',
  '3.5 hours',
  '4 hours',
  '5 hours',
  '6 hours',
  'Half day',
  'Full day',
  '2 days',
  '3 days',
  'Multi-day'
] as const;

// Validation rules
export const ACTIVITY_VALIDATION = {
  title: {
    minLength: 3,
    maxLength: 200,
    required: true
  },
  description: {
    minLength: 10,
    maxLength: 5000,
    required: true
  },
  duration: {
    required: true
  },
  pricing: {
    required: true
  },
  images: {
    minCount: 1,
    maxCount: 10,
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
  }
} as const;
