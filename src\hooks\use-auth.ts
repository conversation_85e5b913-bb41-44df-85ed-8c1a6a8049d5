import { useState, useEffect, createContext, useContext } from "react";
import { supabase } from "@/lib/supabase";
import type { User } from "@supabase/supabase-js";

interface Profile {
  id: string;
  email: string;
  role: "admin" | "staff";
  first_name?: string;
  last_name?: string;
  phone?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface AuthState {
  user: User | null;
  profile: Profile | null;
  loading: boolean;
  initialized: boolean;
}

const AuthContext = createContext<
  AuthState & {
    signIn: (email: string, password: string) => Promise<void>;
    signOut: () => Promise<void>;
    refreshSession: () => Promise<void>;
  }
>({
  user: null,
  profile: null,
  loading: true,
  initialized: false,
  signIn: async () => {},
  signOut: async () => {},
  refreshSession: async () => {},
});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const useAuthState = () => {
  const [state, setState] = useState<AuthState>({
    user: null,
    profile: null,
    loading: true,
    initialized: false,
  });

  const fetchProfile = async (userId: string): Promise<Profile | null> => {
    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", userId)
        .single();

      if (error) {
        console.error("Error fetching profile:", error);
        return null;
      }

      return data;
    } catch (error) {
      console.error("Error fetching profile:", error);
      return null;
    }
  };

  const validateSession = async (): Promise<{
    user: User | null;
    profile: Profile | null;
  }> => {
    try {
      // Get current session from Supabase
      const {
        data: { session },
        error,
      } = await supabase.auth.getSession();

      if (error) {
        console.error("Session validation error:", error);
        return { user: null, profile: null };
      }

      if (!session?.user) {
        return { user: null, profile: null };
      }

      // Validate that the user still exists and is active
      const profile = await fetchProfile(session.user.id);

      if (!profile || !profile.is_active) {
        // User profile doesn't exist or is inactive, sign out
        await supabase.auth.signOut({ scope: "local" });
        return { user: null, profile: null };
      }

      return { user: session.user, profile };
    } catch (error) {
      console.error("Error validating session:", error);
      return { user: null, profile: null };
    }
  };

  const signIn = async (email: string, password: string) => {
    setState((prev) => ({ ...prev, loading: true }));

    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;

      // Session will be handled by the auth state change listener
    } catch (error) {
      setState((prev) => ({ ...prev, loading: false }));
      throw error;
    }
  };

  const signOut = async () => {
    try {
      setState((prev) => ({ ...prev, loading: true }));

      // Clear localStorage manually to ensure complete cleanup
      try {
        localStorage.removeItem("malombo-auth-token");
        localStorage.removeItem(
          "sb-" + supabase.supabaseUrl.split("//")[1] + "-auth-token"
        );
      } catch (storageError) {
        console.warn("Error clearing localStorage:", storageError);
      }

      // Clear local state
      setState({
        user: null,
        profile: null,
        loading: false,
        initialized: true,
      });

      // Sign out from Supabase
      const { error } = await supabase.auth.signOut({ scope: "local" });
      if (error && error.message !== "Auth session missing!") {
        console.error("Supabase signOut error:", error);
      }
    } catch (error) {
      console.error("Error during signOut:", error);
      // Ensure state is cleared even if signOut fails
      setState({
        user: null,
        profile: null,
        loading: false,
        initialized: true,
      });
    }
  };

  const refreshSession = async () => {
    const { user, profile } = await validateSession();
    setState({
      user,
      profile,
      loading: false,
      initialized: true,
    });
  };

  useEffect(() => {
    let mounted = true;
    let initializationTimeout: NodeJS.Timeout;

    // Initialize authentication with timeout to prevent infinite loading
    const initializeAuth = async () => {
      try {
        // Set a maximum timeout for initialization
        initializationTimeout = setTimeout(() => {
          if (mounted) {
            console.warn(
              "Auth initialization timeout - setting to unauthenticated state"
            );
            setState({
              user: null,
              profile: null,
              loading: false,
              initialized: true,
            });
          }
        }, 10000); // 10 second timeout

        // Validate current session
        const { user, profile } = await validateSession();

        if (mounted) {
          clearTimeout(initializationTimeout);
          setState({
            user,
            profile,
            loading: false,
            initialized: true,
          });
        }
      } catch (error) {
        console.error("Error initializing auth:", error);
        if (mounted) {
          clearTimeout(initializationTimeout);
          setState({
            user: null,
            profile: null,
            loading: false,
            initialized: true,
          });
        }
      }
    };

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (!mounted) return;

      console.log("Auth state change:", event, session?.user?.id);

      // Handle different auth events
      switch (event) {
        case "INITIAL_SESSION":
          // Skip - handled by initializeAuth
          break;
        case "SIGNED_IN":
          if (session?.user) {
            const profile = await fetchProfile(session.user.id);
            if (mounted) {
              setState({
                user: session.user,
                profile,
                loading: false,
                initialized: true,
              });
            }
          }
          break;
        case "SIGNED_OUT":
          if (mounted) {
            setState({
              user: null,
              profile: null,
              loading: false,
              initialized: true,
            });
          }
          break;
        case "TOKEN_REFRESHED":
          // Session is still valid, no need to update state
          break;
        default:
          console.log("Unhandled auth event:", event);
      }
    });

    // Initialize auth
    initializeAuth();

    return () => {
      mounted = false;
      if (initializationTimeout) {
        clearTimeout(initializationTimeout);
      }
      subscription.unsubscribe();
    };
  }, []);

  return {
    ...state,
    signIn,
    signOut,
    refreshSession,
  };
};

export { AuthContext };
