import { useState, useEffect, createContext, useContext } from "react";
import { supabase } from "@/lib/supabase";
import type { User } from "@supabase/supabase-js";

interface Profile {
  id: string;
  email: string;
  role: "admin" | "staff";
  first_name?: string;
  last_name?: string;
  phone?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface AuthState {
  user: User | null;
  profile: Profile | null;
  loading: boolean;
}

const AuthContext = createContext<
  AuthState & {
    signIn: (email: string, password: string) => Promise<void>;
    signOut: () => Promise<void>;
  }
>({
  user: null,
  profile: null,
  loading: true,
  signIn: async () => {},
  signOut: async () => {},
});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export const useAuthState = () => {
  const [state, setState] = useState<AuthState>({
    user: null,
    profile: null,
    loading: true,
  });

  const fetchProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", userId)
        .single();

      if (error) {
        console.error("Error fetching profile:", error);
        return null;
      }

      return data;
    } catch (error) {
      console.error("Error fetching profile:", error);
      return null;
    }
  };

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;
  };

  const signOut = async () => {
    try {
      // Clear local state first
      setState((prev) => ({
        ...prev,
        user: null,
        profile: null,
      }));

      // Then sign out from Supabase
      const { error } = await supabase.auth.signOut({ scope: "local" });
      if (error) {
        console.error("Supabase signOut error:", error);
        // Don't throw error for session missing, as user is already logged out locally
        if (error.message !== "Auth session missing!") {
          throw error;
        }
      }
    } catch (error) {
      console.error("Error during signOut:", error);
      // Even if signOut fails, ensure local state is cleared
      setState((prev) => ({
        ...prev,
        user: null,
        profile: null,
      }));
      throw error;
    }
  };

  useEffect(() => {
    let mounted = true;
    let isInitialized = false;

    // Get initial session
    const initializeAuth = async () => {
      try {
        const {
          data: { session },
          error,
        } = await supabase.auth.getSession();

        if (error) {
          console.error("Error getting session:", error);
          if (mounted) {
            setState({
              user: null,
              profile: null,
              loading: false,
            });
          }
          return;
        }

        if (session?.user && mounted) {
          const profile = await fetchProfile(session.user.id);
          setState({
            user: session.user,
            profile,
            loading: false,
          });
        } else if (mounted) {
          setState({
            user: null,
            profile: null,
            loading: false,
          });
        }
      } catch (error) {
        console.error("Error initializing auth:", error);
        if (mounted) {
          setState({
            user: null,
            profile: null,
            loading: false,
          });
        }
      } finally {
        isInitialized = true;
      }
    };

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (!mounted) return;

      // Skip the initial INITIAL_SESSION event to avoid race conditions during initialization
      if (event === "INITIAL_SESSION" && !isInitialized) return;

      if (session?.user) {
        const profile = await fetchProfile(session.user.id);
        setState({
          user: session.user,
          profile,
          loading: false,
        });
      } else {
        setState({
          user: null,
          profile: null,
          loading: false,
        });
      }
    });

    // Initialize auth after setting up the listener
    initializeAuth();

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, []);

  return {
    ...state,
    signIn,
    signOut,
  };
};

export { AuthContext };
