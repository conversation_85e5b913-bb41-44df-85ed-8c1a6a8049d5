-- =====================================================
-- Malombo Admin Panel - Profiles Table Creation
-- =====================================================
-- This file creates the profiles table that extends Supabase Auth
-- with user roles and additional metadata for the admin system.
-- 
-- Safe to run multiple times (idempotent)
-- =====================================================

-- Drop table if it exists (for development purposes)
DROP TABLE IF EXISTS public.profiles CASCADE;

-- Create profiles table
CREATE TABLE public.profiles (
    -- Primary key that references Supabase Auth users
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    
    -- User email (duplicated from auth.users for easier queries)
    email TEXT NOT NULL UNIQUE,
    
    -- User role for access control
    -- 'admin' = full access to all features
    -- 'staff' = limited access, read-only for some sections
    role TEXT NOT NULL CHECK (role IN ('admin', 'staff')) DEFAULT 'staff',
    
    -- Additional user metadata
    first_name TEXT,
    last_name TEXT,
    phone TEXT,
    
    -- Status tracking
    is_active BOOLEAN NOT NULL DEFAULT true,
    last_login_at TIMESTAMP WITH TIME ZONE,
    
    -- Audit timestamps
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_profiles_email ON public.profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_role ON public.profiles(role);
CREATE INDEX IF NOT EXISTS idx_profiles_is_active ON public.profiles(is_active);
CREATE INDEX IF NOT EXISTS idx_profiles_created_at ON public.profiles(created_at);

-- Add comments for documentation
COMMENT ON TABLE public.profiles IS 'User profiles extending Supabase Auth with roles and metadata';
COMMENT ON COLUMN public.profiles.id IS 'References auth.users.id, primary key';
COMMENT ON COLUMN public.profiles.email IS 'User email address, must be unique';
COMMENT ON COLUMN public.profiles.role IS 'User role: admin (full access) or staff (limited access)';
COMMENT ON COLUMN public.profiles.is_active IS 'Whether the user account is active';
COMMENT ON COLUMN public.profiles.last_login_at IS 'Timestamp of last successful login';

-- Grant necessary permissions
GRANT ALL ON public.profiles TO authenticated;
GRANT SELECT ON public.profiles TO anon;

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Profiles table created successfully with indexes and permissions';
END $$;
